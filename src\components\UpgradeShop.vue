<template>
  <div class="upgrade-shop">
    <h3>Marine Ecosystem</h3>
    <div class="upgrades-list">
      <div
        v-for="upgrade in upgrades"
        :key="upgrade.id"
        class="upgrade-item"
        :class="{
          'affordable': canAfford(upgrade),
          'owned': upgrade.owned > 0
        }"
        @click="buyUpgrade(upgrade.id)"
      >
        <div class="upgrade-icon">
          {{ getUpgradeIcon(upgrade.id, upgrade.type) }}
        </div>

        <div class="upgrade-info">
          <div class="upgrade-header">
            <h4>{{ upgrade.name }}</h4>
            <span v-if="upgrade.owned > 0" class="owned-count">{{ upgrade.owned }}</span>
          </div>
          <p class="upgrade-description">{{ upgrade.description }}</p>
          <div class="upgrade-stats">
            <span class="upgrade-effect">
              {{ getEffectText(upgrade) }}
            </span>
          </div>
        </div>

        <div class="upgrade-cost">
          <span class="cost-amount">{{ formatNumber(upgrade.cost) }}</span>
          <span class="cost-currency">🪼</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Upgrade } from '../composables/useGame'

const props = defineProps<{
  upgrades: Upgrade[]
  canAfford: (upgrade: Upgrade) => boolean
  formatNumber: (num: number) => string
}>()

const emit = defineEmits<{
  buyUpgrade: [upgradeId: string]
}>()

const buyUpgrade = (upgradeId: string) => {
  const upgrade = props.upgrades.find(u => u.id === upgradeId)
  if (upgrade && props.canAfford(upgrade)) {
    emit('buyUpgrade', upgradeId)
  }
}

const getUpgradeIcon = (id: string, type: string): string => {
  const icons: Record<string, string> = {
    'plankton': '🦠',
    'polyp': '🌱',
    'coral_reef': '🪸',
    'deep_trench': '🕳️',
    'bio_lab': '🧬',
    'click_upgrade': '✨',
    'multiplier': '🧪'
  }
  return icons[id] || (type === 'auto' ? '🌊' : '⚡')
}

const getEffectText = (upgrade: Upgrade): string => {
  switch (upgrade.type) {
    case 'auto':
      return `+${upgrade.effect} jellyfish/sec`
    case 'click':
      return `${upgrade.effect}x regeneration power`
    case 'multiplier':
      return `${upgrade.effect}x all reproduction`
    default:
      return ''
  }
}
</script>

<style scoped>
.upgrade-shop {
  background: linear-gradient(135deg, rgba(30, 58, 138, 0.1), rgba(6, 182, 212, 0.1));
  backdrop-filter: blur(10px);
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
  max-height: 600px;
  overflow-y: auto;
}

.upgrade-shop h3 {
  margin: 0 0 1rem 0;
  color: #1e40af;
  text-align: center;
  font-size: 1.5rem;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.upgrades-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.upgrade-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  border: 2px solid rgba(148, 163, 184, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 0.6;
}

.upgrade-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2);
}

.upgrade-item.affordable {
  opacity: 1;
  border-color: #06b6d4;
  background: rgba(6, 182, 212, 0.1);
}

.upgrade-item.affordable:hover {
  border-color: #0891b2;
  background: rgba(6, 182, 212, 0.2);
}

.upgrade-item.owned {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
}

.upgrade-icon {
  font-size: 2rem;
  margin-right: 1rem;
  min-width: 3rem;
  text-align: center;
}

.upgrade-info {
  flex: 1;
  min-width: 0;
}

.upgrade-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.upgrade-header h4 {
  margin: 0;
  font-size: 1.1rem;
  color: #333;
}

.owned-count {
  background: #06b6d4;
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
}

.upgrade-description {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  color: #475569;
  line-height: 1.3;
}

.upgrade-stats {
  font-size: 0.85rem;
  color: #0891b2;
  font-weight: bold;
}

.upgrade-cost {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-width: 80px;
}

.cost-amount {
  font-size: 1rem;
  font-weight: bold;
  color: #1e40af;
}

.cost-currency {
  font-size: 1.2rem;
}

/* Scrollbar styling */
.upgrade-shop::-webkit-scrollbar {
  width: 8px;
}

.upgrade-shop::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.upgrade-shop::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.upgrade-shop::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

@media (max-width: 768px) {
  .upgrade-item {
    padding: 0.75rem;
  }
  
  .upgrade-icon {
    font-size: 1.5rem;
    min-width: 2.5rem;
  }
  
  .upgrade-header h4 {
    font-size: 1rem;
  }
  
  .upgrade-description {
    font-size: 0.8rem;
  }
}
</style>
