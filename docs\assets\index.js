(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))n(i);new MutationObserver(i=>{for(const r of i)if(r.type==="childList")for(const o of r.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&n(o)}).observe(document,{childList:!0,subtree:!0});function s(i){const r={};return i.integrity&&(r.integrity=i.integrity),i.referrerPolicy&&(r.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?r.credentials="include":i.crossOrigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function n(i){if(i.ep)return;i.ep=!0;const r=s(i);fetch(i.href,r)}})();/**
* @vue/shared v3.5.16
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Ms(e){const t=Object.create(null);for(const s of e.split(","))t[s]=1;return s=>s in t}const G={},st=[],Oe=()=>{},Ni=()=>!1,qt=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Is=e=>e.startsWith("onUpdate:"),te=Object.assign,js=(e,t)=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)},Hi=Object.prototype.hasOwnProperty,L=(e,t)=>Hi.call(e,t),M=Array.isArray,nt=e=>Yt(e)==="[object Map]",Rn=e=>Yt(e)==="[object Set]",j=e=>typeof e=="function",q=e=>typeof e=="string",Ge=e=>typeof e=="symbol",J=e=>e!==null&&typeof e=="object",Mn=e=>(J(e)||j(e))&&j(e.then)&&j(e.catch),In=Object.prototype.toString,Yt=e=>In.call(e),Li=e=>Yt(e).slice(8,-1),jn=e=>Yt(e)==="[object Object]",Fs=e=>q(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,gt=Ms(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),zt=e=>{const t=Object.create(null);return s=>t[s]||(t[s]=e(s))},Ui=/-(\w)/g,Ke=zt(e=>e.replace(Ui,(t,s)=>s?s.toUpperCase():"")),Bi=/\B([A-Z])/g,Qe=zt(e=>e.replace(Bi,"-$1").toLowerCase()),Fn=zt(e=>e.charAt(0).toUpperCase()+e.slice(1)),cs=zt(e=>e?`on${Fn(e)}`:""),Be=(e,t)=>!Object.is(e,t),fs=(e,...t)=>{for(let s=0;s<e.length;s++)e[s](...t)},$n=(e,t,s,n=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:n,value:s})},Ki=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let sn;const Xt=()=>sn||(sn=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Zt(e){if(M(e)){const t={};for(let s=0;s<e.length;s++){const n=e[s],i=q(n)?ki(n):Zt(n);if(i)for(const r in i)t[r]=i[r]}return t}else if(q(e)||J(e))return e}const Vi=/;(?![^(]*\))/g,Wi=/:([^]+)/,Gi=/\/\*[^]*?\*\//g;function ki(e){const t={};return e.replace(Gi,"").split(Vi).forEach(s=>{if(s){const n=s.split(Wi);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t}function Et(e){let t="";if(q(e))t=e;else if(M(e))for(let s=0;s<e.length;s++){const n=Et(e[s]);n&&(t+=n+" ")}else if(J(e))for(const s in e)e[s]&&(t+=s+" ");return t.trim()}const Ji="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",qi=Ms(Ji);function Dn(e){return!!e||e===""}const Nn=e=>!!(e&&e.__v_isRef===!0),Z=e=>q(e)?e:e==null?"":M(e)||J(e)&&(e.toString===In||!j(e.toString))?Nn(e)?Z(e.value):JSON.stringify(e,Hn,2):String(e),Hn=(e,t)=>Nn(t)?Hn(e,t.value):nt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((s,[n,i],r)=>(s[us(n,r)+" =>"]=i,s),{})}:Rn(t)?{[`Set(${t.size})`]:[...t.values()].map(s=>us(s))}:Ge(t)?us(t):J(t)&&!M(t)&&!jn(t)?String(t):t,us=(e,t="")=>{var s;return Ge(e)?`Symbol(${(s=e.description)!=null?s:t})`:e};/**
* @vue/reactivity v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ce;class Yi{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=ce,!t&&ce&&(this.index=(ce.scopes||(ce.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].pause();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].resume();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].resume()}}run(t){if(this._active){const s=ce;try{return ce=this,t()}finally{ce=s}}}on(){++this._on===1&&(this.prevScope=ce,ce=this)}off(){this._on>0&&--this._on===0&&(ce=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let s,n;for(s=0,n=this.effects.length;s<n;s++)this.effects[s].stop();for(this.effects.length=0,s=0,n=this.cleanups.length;s<n;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,n=this.scopes.length;s<n;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const i=this.parent.scopes.pop();i&&i!==this&&(this.parent.scopes[this.index]=i,i.index=this.index)}this.parent=void 0}}}function zi(){return ce}let W;const as=new WeakSet;class Ln{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,ce&&ce.active&&ce.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,as.has(this)&&(as.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Bn(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,nn(this),Kn(this);const t=W,s=_e;W=this,_e=!0;try{return this.fn()}finally{Vn(this),W=t,_e=s,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Ns(t);this.deps=this.depsTail=void 0,nn(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?as.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){xs(this)&&this.run()}get dirty(){return xs(this)}}let Un=0,mt,_t;function Bn(e,t=!1){if(e.flags|=8,t){e.next=_t,_t=e;return}e.next=mt,mt=e}function $s(){Un++}function Ds(){if(--Un>0)return;if(_t){let t=_t;for(_t=void 0;t;){const s=t.next;t.next=void 0,t.flags&=-9,t=s}}let e;for(;mt;){let t=mt;for(mt=void 0;t;){const s=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(n){e||(e=n)}t=s}}if(e)throw e}function Kn(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Vn(e){let t,s=e.depsTail,n=s;for(;n;){const i=n.prevDep;n.version===-1?(n===s&&(s=i),Ns(n),Xi(n)):t=n,n.dep.activeLink=n.prevActiveLink,n.prevActiveLink=void 0,n=i}e.deps=t,e.depsTail=s}function xs(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Wn(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Wn(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===xt)||(e.globalVersion=xt,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!xs(e))))return;e.flags|=2;const t=e.dep,s=W,n=_e;W=e,_e=!0;try{Kn(e);const i=e.fn(e._value);(t.version===0||Be(i,e._value))&&(e.flags|=128,e._value=i,t.version++)}catch(i){throw t.version++,i}finally{W=s,_e=n,Vn(e),e.flags&=-3}}function Ns(e,t=!1){const{dep:s,prevSub:n,nextSub:i}=e;if(n&&(n.nextSub=i,e.prevSub=void 0),i&&(i.prevSub=n,e.nextSub=void 0),s.subs===e&&(s.subs=n,!n&&s.computed)){s.computed.flags&=-5;for(let r=s.computed.deps;r;r=r.nextDep)Ns(r,!0)}!t&&!--s.sc&&s.map&&s.map.delete(s.key)}function Xi(e){const{prevDep:t,nextDep:s}=e;t&&(t.nextDep=s,e.prevDep=void 0),s&&(s.prevDep=t,e.nextDep=void 0)}let _e=!0;const Gn=[];function Fe(){Gn.push(_e),_e=!1}function $e(){const e=Gn.pop();_e=e===void 0?!0:e}function nn(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const s=W;W=void 0;try{t()}finally{W=s}}}let xt=0;class Zi{constructor(t,s){this.sub=t,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Hs{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!W||!_e||W===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==W)s=this.activeLink=new Zi(W,this),W.deps?(s.prevDep=W.depsTail,W.depsTail.nextDep=s,W.depsTail=s):W.deps=W.depsTail=s,kn(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const n=s.nextDep;n.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=n),s.prevDep=W.depsTail,s.nextDep=void 0,W.depsTail.nextDep=s,W.depsTail=s,W.deps===s&&(W.deps=n)}return s}trigger(t){this.version++,xt++,this.notify(t)}notify(t){$s();try{for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{Ds()}}}function kn(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let n=t.deps;n;n=n.nextDep)kn(n)}const s=e.dep.subs;s!==e&&(e.prevSub=s,s&&(s.nextSub=e)),e.dep.subs=e}}const ws=new WeakMap,Ze=Symbol(""),Ss=Symbol(""),wt=Symbol("");function Q(e,t,s){if(_e&&W){let n=ws.get(e);n||ws.set(e,n=new Map);let i=n.get(s);i||(n.set(s,i=new Hs),i.map=n,i.key=s),i.track()}}function je(e,t,s,n,i,r){const o=ws.get(e);if(!o){xt++;return}const l=f=>{f&&f.trigger()};if($s(),t==="clear")o.forEach(l);else{const f=M(e),d=f&&Fs(s);if(f&&s==="length"){const a=Number(n);o.forEach((h,g)=>{(g==="length"||g===wt||!Ge(g)&&g>=a)&&l(h)})}else switch((s!==void 0||o.has(void 0))&&l(o.get(s)),d&&l(o.get(wt)),t){case"add":f?d&&l(o.get("length")):(l(o.get(Ze)),nt(e)&&l(o.get(Ss)));break;case"delete":f||(l(o.get(Ze)),nt(e)&&l(o.get(Ss)));break;case"set":nt(e)&&l(o.get(Ze));break}}Ds()}function et(e){const t=H(e);return t===e?t:(Q(t,"iterate",wt),ge(e)?t:t.map(X))}function Qt(e){return Q(e=H(e),"iterate",wt),e}const Qi={__proto__:null,[Symbol.iterator](){return ds(this,Symbol.iterator,X)},concat(...e){return et(this).concat(...e.map(t=>M(t)?et(t):t))},entries(){return ds(this,"entries",e=>(e[1]=X(e[1]),e))},every(e,t){return Re(this,"every",e,t,void 0,arguments)},filter(e,t){return Re(this,"filter",e,t,s=>s.map(X),arguments)},find(e,t){return Re(this,"find",e,t,X,arguments)},findIndex(e,t){return Re(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Re(this,"findLast",e,t,X,arguments)},findLastIndex(e,t){return Re(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Re(this,"forEach",e,t,void 0,arguments)},includes(...e){return hs(this,"includes",e)},indexOf(...e){return hs(this,"indexOf",e)},join(e){return et(this).join(e)},lastIndexOf(...e){return hs(this,"lastIndexOf",e)},map(e,t){return Re(this,"map",e,t,void 0,arguments)},pop(){return dt(this,"pop")},push(...e){return dt(this,"push",e)},reduce(e,...t){return rn(this,"reduce",e,t)},reduceRight(e,...t){return rn(this,"reduceRight",e,t)},shift(){return dt(this,"shift")},some(e,t){return Re(this,"some",e,t,void 0,arguments)},splice(...e){return dt(this,"splice",e)},toReversed(){return et(this).toReversed()},toSorted(e){return et(this).toSorted(e)},toSpliced(...e){return et(this).toSpliced(...e)},unshift(...e){return dt(this,"unshift",e)},values(){return ds(this,"values",X)}};function ds(e,t,s){const n=Qt(e),i=n[t]();return n!==e&&!ge(e)&&(i._next=i.next,i.next=()=>{const r=i._next();return r.value&&(r.value=s(r.value)),r}),i}const er=Array.prototype;function Re(e,t,s,n,i,r){const o=Qt(e),l=o!==e&&!ge(e),f=o[t];if(f!==er[t]){const h=f.apply(e,r);return l?X(h):h}let d=s;o!==e&&(l?d=function(h,g){return s.call(this,X(h),g,e)}:s.length>2&&(d=function(h,g){return s.call(this,h,g,e)}));const a=f.call(o,d,n);return l&&i?i(a):a}function rn(e,t,s,n){const i=Qt(e);let r=s;return i!==e&&(ge(e)?s.length>3&&(r=function(o,l,f){return s.call(this,o,l,f,e)}):r=function(o,l,f){return s.call(this,o,X(l),f,e)}),i[t](r,...n)}function hs(e,t,s){const n=H(e);Q(n,"iterate",wt);const i=n[t](...s);return(i===-1||i===!1)&&Ks(s[0])?(s[0]=H(s[0]),n[t](...s)):i}function dt(e,t,s=[]){Fe(),$s();const n=H(e)[t].apply(e,s);return Ds(),$e(),n}const tr=Ms("__proto__,__v_isRef,__isVue"),Jn=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Ge));function sr(e){Ge(e)||(e=String(e));const t=H(this);return Q(t,"has",e),t.hasOwnProperty(e)}class qn{constructor(t=!1,s=!1){this._isReadonly=t,this._isShallow=s}get(t,s,n){if(s==="__v_skip")return t.__v_skip;const i=this._isReadonly,r=this._isShallow;if(s==="__v_isReactive")return!i;if(s==="__v_isReadonly")return i;if(s==="__v_isShallow")return r;if(s==="__v_raw")return n===(i?r?dr:Zn:r?Xn:zn).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(n)?t:void 0;const o=M(t);if(!i){let f;if(o&&(f=Qi[s]))return f;if(s==="hasOwnProperty")return sr}const l=Reflect.get(t,s,ee(t)?t:n);return(Ge(s)?Jn.has(s):tr(s))||(i||Q(t,"get",s),r)?l:ee(l)?o&&Fs(s)?l:l.value:J(l)?i?Qn(l):Us(l):l}}class Yn extends qn{constructor(t=!1){super(!1,t)}set(t,s,n,i){let r=t[s];if(!this._isShallow){const f=Ve(r);if(!ge(n)&&!Ve(n)&&(r=H(r),n=H(n)),!M(t)&&ee(r)&&!ee(n))return f?!1:(r.value=n,!0)}const o=M(t)&&Fs(s)?Number(s)<t.length:L(t,s),l=Reflect.set(t,s,n,ee(t)?t:i);return t===H(i)&&(o?Be(n,r)&&je(t,"set",s,n):je(t,"add",s,n)),l}deleteProperty(t,s){const n=L(t,s);t[s];const i=Reflect.deleteProperty(t,s);return i&&n&&je(t,"delete",s,void 0),i}has(t,s){const n=Reflect.has(t,s);return(!Ge(s)||!Jn.has(s))&&Q(t,"has",s),n}ownKeys(t){return Q(t,"iterate",M(t)?"length":Ze),Reflect.ownKeys(t)}}class nr extends qn{constructor(t=!1){super(!0,t)}set(t,s){return!0}deleteProperty(t,s){return!0}}const ir=new Yn,rr=new nr,or=new Yn(!0);const Cs=e=>e,Ft=e=>Reflect.getPrototypeOf(e);function lr(e,t,s){return function(...n){const i=this.__v_raw,r=H(i),o=nt(r),l=e==="entries"||e===Symbol.iterator&&o,f=e==="keys"&&o,d=i[e](...n),a=s?Cs:t?Ut:X;return!t&&Q(r,"iterate",f?Ss:Ze),{next(){const{value:h,done:g}=d.next();return g?{value:h,done:g}:{value:l?[a(h[0]),a(h[1])]:a(h),done:g}},[Symbol.iterator](){return this}}}}function $t(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function cr(e,t){const s={get(i){const r=this.__v_raw,o=H(r),l=H(i);e||(Be(i,l)&&Q(o,"get",i),Q(o,"get",l));const{has:f}=Ft(o),d=t?Cs:e?Ut:X;if(f.call(o,i))return d(r.get(i));if(f.call(o,l))return d(r.get(l));r!==o&&r.get(i)},get size(){const i=this.__v_raw;return!e&&Q(H(i),"iterate",Ze),Reflect.get(i,"size",i)},has(i){const r=this.__v_raw,o=H(r),l=H(i);return e||(Be(i,l)&&Q(o,"has",i),Q(o,"has",l)),i===l?r.has(i):r.has(i)||r.has(l)},forEach(i,r){const o=this,l=o.__v_raw,f=H(l),d=t?Cs:e?Ut:X;return!e&&Q(f,"iterate",Ze),l.forEach((a,h)=>i.call(r,d(a),d(h),o))}};return te(s,e?{add:$t("add"),set:$t("set"),delete:$t("delete"),clear:$t("clear")}:{add(i){!t&&!ge(i)&&!Ve(i)&&(i=H(i));const r=H(this);return Ft(r).has.call(r,i)||(r.add(i),je(r,"add",i,i)),this},set(i,r){!t&&!ge(r)&&!Ve(r)&&(r=H(r));const o=H(this),{has:l,get:f}=Ft(o);let d=l.call(o,i);d||(i=H(i),d=l.call(o,i));const a=f.call(o,i);return o.set(i,r),d?Be(r,a)&&je(o,"set",i,r):je(o,"add",i,r),this},delete(i){const r=H(this),{has:o,get:l}=Ft(r);let f=o.call(r,i);f||(i=H(i),f=o.call(r,i)),l&&l.call(r,i);const d=r.delete(i);return f&&je(r,"delete",i,void 0),d},clear(){const i=H(this),r=i.size!==0,o=i.clear();return r&&je(i,"clear",void 0,void 0),o}}),["keys","values","entries",Symbol.iterator].forEach(i=>{s[i]=lr(i,e,t)}),s}function Ls(e,t){const s=cr(e,t);return(n,i,r)=>i==="__v_isReactive"?!e:i==="__v_isReadonly"?e:i==="__v_raw"?n:Reflect.get(L(s,i)&&i in n?s:n,i,r)}const fr={get:Ls(!1,!1)},ur={get:Ls(!1,!0)},ar={get:Ls(!0,!1)};const zn=new WeakMap,Xn=new WeakMap,Zn=new WeakMap,dr=new WeakMap;function hr(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function pr(e){return e.__v_skip||!Object.isExtensible(e)?0:hr(Li(e))}function Us(e){return Ve(e)?e:Bs(e,!1,ir,fr,zn)}function gr(e){return Bs(e,!1,or,ur,Xn)}function Qn(e){return Bs(e,!0,rr,ar,Zn)}function Bs(e,t,s,n,i){if(!J(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const r=pr(e);if(r===0)return e;const o=i.get(e);if(o)return o;const l=new Proxy(e,r===2?n:s);return i.set(e,l),l}function it(e){return Ve(e)?it(e.__v_raw):!!(e&&e.__v_isReactive)}function Ve(e){return!!(e&&e.__v_isReadonly)}function ge(e){return!!(e&&e.__v_isShallow)}function Ks(e){return e?!!e.__v_raw:!1}function H(e){const t=e&&e.__v_raw;return t?H(t):e}function mr(e){return!L(e,"__v_skip")&&Object.isExtensible(e)&&$n(e,"__v_skip",!0),e}const X=e=>J(e)?Us(e):e,Ut=e=>J(e)?Qn(e):e;function ee(e){return e?e.__v_isRef===!0:!1}function Ie(e){return _r(e,!1)}function _r(e,t){return ee(e)?e:new yr(e,t)}class yr{constructor(t,s){this.dep=new Hs,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?t:H(t),this._value=s?t:X(t),this.__v_isShallow=s}get value(){return this.dep.track(),this._value}set value(t){const s=this._rawValue,n=this.__v_isShallow||ge(t)||Ve(t);t=n?t:H(t),Be(t,s)&&(this._rawValue=t,this._value=n?t:X(t),this.dep.trigger())}}function z(e){return ee(e)?e.value:e}const br={get:(e,t,s)=>t==="__v_raw"?e:z(Reflect.get(e,t,s)),set:(e,t,s,n)=>{const i=e[t];return ee(i)&&!ee(s)?(i.value=s,!0):Reflect.set(e,t,s,n)}};function ei(e){return it(e)?e:new Proxy(e,br)}class vr{constructor(t,s,n){this.fn=t,this.setter=s,this._value=void 0,this.dep=new Hs(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=xt-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=n}notify(){if(this.flags|=16,!(this.flags&8)&&W!==this)return Bn(this,!0),!0}get value(){const t=this.dep.track();return Wn(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function xr(e,t,s=!1){let n,i;return j(e)?n=e:(n=e.get,i=e.set),new vr(n,i,s)}const Dt={},Bt=new WeakMap;let Xe;function wr(e,t=!1,s=Xe){if(s){let n=Bt.get(s);n||Bt.set(s,n=[]),n.push(e)}}function Sr(e,t,s=G){const{immediate:n,deep:i,once:r,scheduler:o,augmentJob:l,call:f}=s,d=E=>i?E:ge(E)||i===!1||i===0?Ue(E,1):Ue(E);let a,h,g,C,F=!1,$=!1;if(ee(e)?(h=()=>e.value,F=ge(e)):it(e)?(h=()=>d(e),F=!0):M(e)?($=!0,F=e.some(E=>it(E)||ge(E)),h=()=>e.map(E=>{if(ee(E))return E.value;if(it(E))return d(E);if(j(E))return f?f(E,2):E()})):j(e)?t?h=f?()=>f(e,2):e:h=()=>{if(g){Fe();try{g()}finally{$e()}}const E=Xe;Xe=a;try{return f?f(e,3,[C]):e(C)}finally{Xe=E}}:h=Oe,t&&i){const E=h,N=i===!0?1/0:i;h=()=>Ue(E(),N)}const Y=zi(),U=()=>{a.stop(),Y&&Y.active&&js(Y.effects,a)};if(r&&t){const E=t;t=(...N)=>{E(...N),U()}}let T=$?new Array(e.length).fill(Dt):Dt;const I=E=>{if(!(!(a.flags&1)||!a.dirty&&!E))if(t){const N=a.run();if(i||F||($?N.some((ye,be)=>Be(ye,T[be])):Be(N,T))){g&&g();const ye=Xe;Xe=a;try{const be=[N,T===Dt?void 0:$&&T[0]===Dt?[]:T,C];T=N,f?f(t,3,be):t(...be)}finally{Xe=ye}}}else a.run()};return l&&l(I),a=new Ln(h),a.scheduler=o?()=>o(I,!1):I,C=E=>wr(E,!1,a),g=a.onStop=()=>{const E=Bt.get(a);if(E){if(f)f(E,4);else for(const N of E)N();Bt.delete(a)}},t?n?I(!0):T=a.run():o?o(I.bind(null,!0),!0):a.run(),U.pause=a.pause.bind(a),U.resume=a.resume.bind(a),U.stop=U,U}function Ue(e,t=1/0,s){if(t<=0||!J(e)||e.__v_skip||(s=s||new Set,s.has(e)))return e;if(s.add(e),t--,ee(e))Ue(e.value,t,s);else if(M(e))for(let n=0;n<e.length;n++)Ue(e[n],t,s);else if(Rn(e)||nt(e))e.forEach(n=>{Ue(n,t,s)});else if(jn(e)){for(const n in e)Ue(e[n],t,s);for(const n of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,n)&&Ue(e[n],t,s)}return e}/**
* @vue/runtime-core v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function At(e,t,s,n){try{return n?e(...n):e()}catch(i){es(i,t,s)}}function Pe(e,t,s,n){if(j(e)){const i=At(e,t,s,n);return i&&Mn(i)&&i.catch(r=>{es(r,t,s)}),i}if(M(e)){const i=[];for(let r=0;r<e.length;r++)i.push(Pe(e[r],t,s,n));return i}}function es(e,t,s,n=!0){const i=t?t.vnode:null,{errorHandler:r,throwUnhandledErrorInProduction:o}=t&&t.appContext.config||G;if(t){let l=t.parent;const f=t.proxy,d=`https://vuejs.org/error-reference/#runtime-${s}`;for(;l;){const a=l.ec;if(a){for(let h=0;h<a.length;h++)if(a[h](e,f,d)===!1)return}l=l.parent}if(r){Fe(),At(r,null,10,[e,f,d]),$e();return}}Cr(e,s,i,n,o)}function Cr(e,t,s,n=!0,i=!1){if(i)throw e;console.error(e)}const ie=[];let Te=-1;const rt=[];let He=null,tt=0;const ti=Promise.resolve();let Kt=null;function Tr(e){const t=Kt||ti;return e?t.then(this?e.bind(this):e):t}function Er(e){let t=Te+1,s=ie.length;for(;t<s;){const n=t+s>>>1,i=ie[n],r=St(i);r<e||r===e&&i.flags&2?t=n+1:s=n}return t}function Vs(e){if(!(e.flags&1)){const t=St(e),s=ie[ie.length-1];!s||!(e.flags&2)&&t>=St(s)?ie.push(e):ie.splice(Er(t),0,e),e.flags|=1,si()}}function si(){Kt||(Kt=ti.then(ii))}function Ar(e){M(e)?rt.push(...e):He&&e.id===-1?He.splice(tt+1,0,e):e.flags&1||(rt.push(e),e.flags|=1),si()}function on(e,t,s=Te+1){for(;s<ie.length;s++){const n=ie[s];if(n&&n.flags&2){if(e&&n.id!==e.uid)continue;ie.splice(s,1),s--,n.flags&4&&(n.flags&=-2),n(),n.flags&4||(n.flags&=-2)}}}function ni(e){if(rt.length){const t=[...new Set(rt)].sort((s,n)=>St(s)-St(n));if(rt.length=0,He){He.push(...t);return}for(He=t,tt=0;tt<He.length;tt++){const s=He[tt];s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2}He=null,tt=0}}const St=e=>e.id==null?e.flags&2?-1:1/0:e.id;function ii(e){try{for(Te=0;Te<ie.length;Te++){const t=ie[Te];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),At(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Te<ie.length;Te++){const t=ie[Te];t&&(t.flags&=-2)}Te=-1,ie.length=0,ni(),Kt=null,(ie.length||rt.length)&&ii()}}let Ae=null,ri=null;function Vt(e){const t=Ae;return Ae=e,ri=e&&e.type.__scopeId||null,t}function Or(e,t=Ae,s){if(!t||e._n)return e;const n=(...i)=>{n._d&&gn(-1);const r=Vt(t);let o;try{o=e(...i)}finally{Vt(r),n._d&&gn(1)}return o};return n._n=!0,n._c=!0,n._d=!0,n}function Ye(e,t,s,n){const i=e.dirs,r=t&&t.dirs;for(let o=0;o<i.length;o++){const l=i[o];r&&(l.oldValue=r[o].value);let f=l.dir[n];f&&(Fe(),Pe(f,s,8,[e.el,l,e,t]),$e())}}const Pr=Symbol("_vte"),Rr=e=>e.__isTeleport;function Ws(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Ws(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function ts(e,t){return j(e)?te({name:e.name},t,{setup:e}):e}function oi(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Wt(e,t,s,n,i=!1){if(M(e)){e.forEach((F,$)=>Wt(F,t&&(M(t)?t[$]:t),s,n,i));return}if(yt(n)&&!i){n.shapeFlag&512&&n.type.__asyncResolved&&n.component.subTree.component&&Wt(e,t,s,n.component.subTree);return}const r=n.shapeFlag&4?zs(n.component):n.el,o=i?null:r,{i:l,r:f}=e,d=t&&t.r,a=l.refs===G?l.refs={}:l.refs,h=l.setupState,g=H(h),C=h===G?()=>!1:F=>L(g,F);if(d!=null&&d!==f&&(q(d)?(a[d]=null,C(d)&&(h[d]=null)):ee(d)&&(d.value=null)),j(f))At(f,l,12,[o,a]);else{const F=q(f),$=ee(f);if(F||$){const Y=()=>{if(e.f){const U=F?C(f)?h[f]:a[f]:f.value;i?M(U)&&js(U,r):M(U)?U.includes(r)||U.push(r):F?(a[f]=[r],C(f)&&(h[f]=a[f])):(f.value=[r],e.k&&(a[e.k]=f.value))}else F?(a[f]=o,C(f)&&(h[f]=o)):$&&(f.value=o,e.k&&(a[e.k]=o))};o?(Y.id=-1,de(Y,s)):Y()}}}Xt().requestIdleCallback;Xt().cancelIdleCallback;const yt=e=>!!e.type.__asyncLoader,li=e=>e.type.__isKeepAlive;function Mr(e,t){ci(e,"a",t)}function Ir(e,t){ci(e,"da",t)}function ci(e,t,s=oe){const n=e.__wdc||(e.__wdc=()=>{let i=s;for(;i;){if(i.isDeactivated)return;i=i.parent}return e()});if(ss(t,n,s),s){let i=s.parent;for(;i&&i.parent;)li(i.parent.vnode)&&jr(n,t,s,i),i=i.parent}}function jr(e,t,s,n){const i=ss(t,e,n,!0);Gs(()=>{js(n[t],i)},s)}function ss(e,t,s=oe,n=!1){if(s){const i=s[e]||(s[e]=[]),r=t.__weh||(t.__weh=(...o)=>{Fe();const l=Ot(s),f=Pe(t,s,e,o);return l(),$e(),f});return n?i.unshift(r):i.push(r),r}}const De=e=>(t,s=oe)=>{(!Tt||e==="sp")&&ss(e,(...n)=>t(...n),s)},Fr=De("bm"),fi=De("m"),$r=De("bu"),Dr=De("u"),Nr=De("bum"),Gs=De("um"),Hr=De("sp"),Lr=De("rtg"),Ur=De("rtc");function Br(e,t=oe){ss("ec",e,t)}const Kr=Symbol.for("v-ndc");function ks(e,t,s,n){let i;const r=s,o=M(e);if(o||q(e)){const l=o&&it(e);let f=!1,d=!1;l&&(f=!ge(e),d=Ve(e),e=Qt(e)),i=new Array(e.length);for(let a=0,h=e.length;a<h;a++)i[a]=t(f?d?Ut(X(e[a])):X(e[a]):e[a],a,void 0,r)}else if(typeof e=="number"){i=new Array(e);for(let l=0;l<e;l++)i[l]=t(l+1,l,void 0,r)}else if(J(e))if(e[Symbol.iterator])i=Array.from(e,(l,f)=>t(l,f,void 0,r));else{const l=Object.keys(e);i=new Array(l.length);for(let f=0,d=l.length;f<d;f++){const a=l[f];i[f]=t(e[a],a,f,r)}}else i=[];return i}const Ts=e=>e?Mi(e)?zs(e):Ts(e.parent):null,bt=te(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Ts(e.parent),$root:e=>Ts(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>ai(e),$forceUpdate:e=>e.f||(e.f=()=>{Vs(e.update)}),$nextTick:e=>e.n||(e.n=Tr.bind(e.proxy)),$watch:e=>uo.bind(e)}),ps=(e,t)=>e!==G&&!e.__isScriptSetup&&L(e,t),Vr={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:s,setupState:n,data:i,props:r,accessCache:o,type:l,appContext:f}=e;let d;if(t[0]!=="$"){const C=o[t];if(C!==void 0)switch(C){case 1:return n[t];case 2:return i[t];case 4:return s[t];case 3:return r[t]}else{if(ps(n,t))return o[t]=1,n[t];if(i!==G&&L(i,t))return o[t]=2,i[t];if((d=e.propsOptions[0])&&L(d,t))return o[t]=3,r[t];if(s!==G&&L(s,t))return o[t]=4,s[t];Es&&(o[t]=0)}}const a=bt[t];let h,g;if(a)return t==="$attrs"&&Q(e.attrs,"get",""),a(e);if((h=l.__cssModules)&&(h=h[t]))return h;if(s!==G&&L(s,t))return o[t]=4,s[t];if(g=f.config.globalProperties,L(g,t))return g[t]},set({_:e},t,s){const{data:n,setupState:i,ctx:r}=e;return ps(i,t)?(i[t]=s,!0):n!==G&&L(n,t)?(n[t]=s,!0):L(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(r[t]=s,!0)},has({_:{data:e,setupState:t,accessCache:s,ctx:n,appContext:i,propsOptions:r}},o){let l;return!!s[o]||e!==G&&L(e,o)||ps(t,o)||(l=r[0])&&L(l,o)||L(n,o)||L(bt,o)||L(i.config.globalProperties,o)},defineProperty(e,t,s){return s.get!=null?e._.accessCache[t]=0:L(s,"value")&&this.set(e,t,s.value,null),Reflect.defineProperty(e,t,s)}};function ln(e){return M(e)?e.reduce((t,s)=>(t[s]=null,t),{}):e}let Es=!0;function Wr(e){const t=ai(e),s=e.proxy,n=e.ctx;Es=!1,t.beforeCreate&&cn(t.beforeCreate,e,"bc");const{data:i,computed:r,methods:o,watch:l,provide:f,inject:d,created:a,beforeMount:h,mounted:g,beforeUpdate:C,updated:F,activated:$,deactivated:Y,beforeDestroy:U,beforeUnmount:T,destroyed:I,unmounted:E,render:N,renderTracked:ye,renderTriggered:be,errorCaptured:Ne,serverPrefetch:Pt,expose:ke,inheritAttrs:ct,components:Rt,directives:Mt,filters:os}=t;if(d&&Gr(d,n,null),o)for(const k in o){const K=o[k];j(K)&&(n[k]=K.bind(s))}if(i){const k=i.call(s,s);J(k)&&(e.data=Us(k))}if(Es=!0,r)for(const k in r){const K=r[k],Je=j(K)?K.bind(s,s):j(K.get)?K.get.bind(s,s):Oe,It=!j(K)&&j(K.set)?K.set.bind(s):Oe,qe=ji({get:Je,set:It});Object.defineProperty(n,k,{enumerable:!0,configurable:!0,get:()=>qe.value,set:ve=>qe.value=ve})}if(l)for(const k in l)ui(l[k],n,s,k);if(f){const k=j(f)?f.call(s):f;Reflect.ownKeys(k).forEach(K=>{Xr(K,k[K])})}a&&cn(a,e,"c");function se(k,K){M(K)?K.forEach(Je=>k(Je.bind(s))):K&&k(K.bind(s))}if(se(Fr,h),se(fi,g),se($r,C),se(Dr,F),se(Mr,$),se(Ir,Y),se(Br,Ne),se(Ur,ye),se(Lr,be),se(Nr,T),se(Gs,E),se(Hr,Pt),M(ke))if(ke.length){const k=e.exposed||(e.exposed={});ke.forEach(K=>{Object.defineProperty(k,K,{get:()=>s[K],set:Je=>s[K]=Je})})}else e.exposed||(e.exposed={});N&&e.render===Oe&&(e.render=N),ct!=null&&(e.inheritAttrs=ct),Rt&&(e.components=Rt),Mt&&(e.directives=Mt),Pt&&oi(e)}function Gr(e,t,s=Oe){M(e)&&(e=As(e));for(const n in e){const i=e[n];let r;J(i)?"default"in i?r=Nt(i.from||n,i.default,!0):r=Nt(i.from||n):r=Nt(i),ee(r)?Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>r.value,set:o=>r.value=o}):t[n]=r}}function cn(e,t,s){Pe(M(e)?e.map(n=>n.bind(t.proxy)):e.bind(t.proxy),t,s)}function ui(e,t,s,n){let i=n.includes(".")?Ci(s,n):()=>s[n];if(q(e)){const r=t[e];j(r)&&ms(i,r)}else if(j(e))ms(i,e.bind(s));else if(J(e))if(M(e))e.forEach(r=>ui(r,t,s,n));else{const r=j(e.handler)?e.handler.bind(s):t[e.handler];j(r)&&ms(i,r,e)}}function ai(e){const t=e.type,{mixins:s,extends:n}=t,{mixins:i,optionsCache:r,config:{optionMergeStrategies:o}}=e.appContext,l=r.get(t);let f;return l?f=l:!i.length&&!s&&!n?f=t:(f={},i.length&&i.forEach(d=>Gt(f,d,o,!0)),Gt(f,t,o)),J(t)&&r.set(t,f),f}function Gt(e,t,s,n=!1){const{mixins:i,extends:r}=t;r&&Gt(e,r,s,!0),i&&i.forEach(o=>Gt(e,o,s,!0));for(const o in t)if(!(n&&o==="expose")){const l=kr[o]||s&&s[o];e[o]=l?l(e[o],t[o]):t[o]}return e}const kr={data:fn,props:un,emits:un,methods:pt,computed:pt,beforeCreate:ne,created:ne,beforeMount:ne,mounted:ne,beforeUpdate:ne,updated:ne,beforeDestroy:ne,beforeUnmount:ne,destroyed:ne,unmounted:ne,activated:ne,deactivated:ne,errorCaptured:ne,serverPrefetch:ne,components:pt,directives:pt,watch:qr,provide:fn,inject:Jr};function fn(e,t){return t?e?function(){return te(j(e)?e.call(this,this):e,j(t)?t.call(this,this):t)}:t:e}function Jr(e,t){return pt(As(e),As(t))}function As(e){if(M(e)){const t={};for(let s=0;s<e.length;s++)t[e[s]]=e[s];return t}return e}function ne(e,t){return e?[...new Set([].concat(e,t))]:t}function pt(e,t){return e?te(Object.create(null),e,t):t}function un(e,t){return e?M(e)&&M(t)?[...new Set([...e,...t])]:te(Object.create(null),ln(e),ln(t??{})):t}function qr(e,t){if(!e)return t;if(!t)return e;const s=te(Object.create(null),e);for(const n in t)s[n]=ne(e[n],t[n]);return s}function di(){return{app:null,config:{isNativeTag:Ni,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Yr=0;function zr(e,t){return function(n,i=null){j(n)||(n=te({},n)),i!=null&&!J(i)&&(i=null);const r=di(),o=new WeakSet,l=[];let f=!1;const d=r.app={_uid:Yr++,_component:n,_props:i,_container:null,_context:r,_instance:null,version:Io,get config(){return r.config},set config(a){},use(a,...h){return o.has(a)||(a&&j(a.install)?(o.add(a),a.install(d,...h)):j(a)&&(o.add(a),a(d,...h))),d},mixin(a){return r.mixins.includes(a)||r.mixins.push(a),d},component(a,h){return h?(r.components[a]=h,d):r.components[a]},directive(a,h){return h?(r.directives[a]=h,d):r.directives[a]},mount(a,h,g){if(!f){const C=d._ceVNode||me(n,i);return C.appContext=r,g===!0?g="svg":g===!1&&(g=void 0),e(C,a,g),f=!0,d._container=a,a.__vue_app__=d,zs(C.component)}},onUnmount(a){l.push(a)},unmount(){f&&(Pe(l,d._instance,16),e(null,d._container),delete d._container.__vue_app__)},provide(a,h){return r.provides[a]=h,d},runWithContext(a){const h=ot;ot=d;try{return a()}finally{ot=h}}};return d}}let ot=null;function Xr(e,t){if(oe){let s=oe.provides;const n=oe.parent&&oe.parent.provides;n===s&&(s=oe.provides=Object.create(n)),s[e]=t}}function Nt(e,t,s=!1){const n=oe||Ae;if(n||ot){let i=ot?ot._context.provides:n?n.parent==null||n.ce?n.vnode.appContext&&n.vnode.appContext.provides:n.parent.provides:void 0;if(i&&e in i)return i[e];if(arguments.length>1)return s&&j(t)?t.call(n&&n.proxy):t}}const hi={},pi=()=>Object.create(hi),gi=e=>Object.getPrototypeOf(e)===hi;function Zr(e,t,s,n=!1){const i={},r=pi();e.propsDefaults=Object.create(null),mi(e,t,i,r);for(const o in e.propsOptions[0])o in i||(i[o]=void 0);s?e.props=n?i:gr(i):e.type.props?e.props=i:e.props=r,e.attrs=r}function Qr(e,t,s,n){const{props:i,attrs:r,vnode:{patchFlag:o}}=e,l=H(i),[f]=e.propsOptions;let d=!1;if((n||o>0)&&!(o&16)){if(o&8){const a=e.vnode.dynamicProps;for(let h=0;h<a.length;h++){let g=a[h];if(ns(e.emitsOptions,g))continue;const C=t[g];if(f)if(L(r,g))C!==r[g]&&(r[g]=C,d=!0);else{const F=Ke(g);i[F]=Os(f,l,F,C,e,!1)}else C!==r[g]&&(r[g]=C,d=!0)}}}else{mi(e,t,i,r)&&(d=!0);let a;for(const h in l)(!t||!L(t,h)&&((a=Qe(h))===h||!L(t,a)))&&(f?s&&(s[h]!==void 0||s[a]!==void 0)&&(i[h]=Os(f,l,h,void 0,e,!0)):delete i[h]);if(r!==l)for(const h in r)(!t||!L(t,h))&&(delete r[h],d=!0)}d&&je(e.attrs,"set","")}function mi(e,t,s,n){const[i,r]=e.propsOptions;let o=!1,l;if(t)for(let f in t){if(gt(f))continue;const d=t[f];let a;i&&L(i,a=Ke(f))?!r||!r.includes(a)?s[a]=d:(l||(l={}))[a]=d:ns(e.emitsOptions,f)||(!(f in n)||d!==n[f])&&(n[f]=d,o=!0)}if(r){const f=H(s),d=l||G;for(let a=0;a<r.length;a++){const h=r[a];s[h]=Os(i,f,h,d[h],e,!L(d,h))}}return o}function Os(e,t,s,n,i,r){const o=e[s];if(o!=null){const l=L(o,"default");if(l&&n===void 0){const f=o.default;if(o.type!==Function&&!o.skipFactory&&j(f)){const{propsDefaults:d}=i;if(s in d)n=d[s];else{const a=Ot(i);n=d[s]=f.call(null,t),a()}}else n=f;i.ce&&i.ce._setProp(s,n)}o[0]&&(r&&!l?n=!1:o[1]&&(n===""||n===Qe(s))&&(n=!0))}return n}const eo=new WeakMap;function _i(e,t,s=!1){const n=s?eo:t.propsCache,i=n.get(e);if(i)return i;const r=e.props,o={},l=[];let f=!1;if(!j(e)){const a=h=>{f=!0;const[g,C]=_i(h,t,!0);te(o,g),C&&l.push(...C)};!s&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}if(!r&&!f)return J(e)&&n.set(e,st),st;if(M(r))for(let a=0;a<r.length;a++){const h=Ke(r[a]);an(h)&&(o[h]=G)}else if(r)for(const a in r){const h=Ke(a);if(an(h)){const g=r[a],C=o[h]=M(g)||j(g)?{type:g}:te({},g),F=C.type;let $=!1,Y=!0;if(M(F))for(let U=0;U<F.length;++U){const T=F[U],I=j(T)&&T.name;if(I==="Boolean"){$=!0;break}else I==="String"&&(Y=!1)}else $=j(F)&&F.name==="Boolean";C[0]=$,C[1]=Y,($||L(C,"default"))&&l.push(h)}}const d=[o,l];return J(e)&&n.set(e,d),d}function an(e){return e[0]!=="$"&&!gt(e)}const Js=e=>e[0]==="_"||e==="$stable",qs=e=>M(e)?e.map(Ee):[Ee(e)],to=(e,t,s)=>{if(t._n)return t;const n=Or((...i)=>qs(t(...i)),s);return n._c=!1,n},yi=(e,t,s)=>{const n=e._ctx;for(const i in e){if(Js(i))continue;const r=e[i];if(j(r))t[i]=to(i,r,n);else if(r!=null){const o=qs(r);t[i]=()=>o}}},bi=(e,t)=>{const s=qs(t);e.slots.default=()=>s},vi=(e,t,s)=>{for(const n in t)(s||!Js(n))&&(e[n]=t[n])},so=(e,t,s)=>{const n=e.slots=pi();if(e.vnode.shapeFlag&32){const i=t._;i?(vi(n,t,s),s&&$n(n,"_",i,!0)):yi(t,n)}else t&&bi(e,t)},no=(e,t,s)=>{const{vnode:n,slots:i}=e;let r=!0,o=G;if(n.shapeFlag&32){const l=t._;l?s&&l===1?r=!1:vi(i,t,s):(r=!t.$stable,yi(t,i)),o=t}else t&&(bi(e,t),o={default:1});if(r)for(const l in i)!Js(l)&&o[l]==null&&delete i[l]},de=yo;function io(e){return ro(e)}function ro(e,t){const s=Xt();s.__VUE__=!0;const{insert:n,remove:i,patchProp:r,createElement:o,createText:l,createComment:f,setText:d,setElementText:a,parentNode:h,nextSibling:g,setScopeId:C=Oe,insertStaticContent:F}=e,$=(c,u,p,y=null,m=null,_=null,w=void 0,x=null,v=!!u.dynamicChildren)=>{if(c===u)return;c&&!ht(c,u)&&(y=jt(c),ve(c,m,_,!0),c=null),u.patchFlag===-2&&(v=!1,u.dynamicChildren=null);const{type:b,ref:O,shapeFlag:S}=u;switch(b){case is:Y(c,u,p,y);break;case We:U(c,u,p,y);break;case _s:c==null&&T(u,p,y,w);break;case pe:Rt(c,u,p,y,m,_,w,x,v);break;default:S&1?N(c,u,p,y,m,_,w,x,v):S&6?Mt(c,u,p,y,m,_,w,x,v):(S&64||S&128)&&b.process(c,u,p,y,m,_,w,x,v,ut)}O!=null&&m&&Wt(O,c&&c.ref,_,u||c,!u)},Y=(c,u,p,y)=>{if(c==null)n(u.el=l(u.children),p,y);else{const m=u.el=c.el;u.children!==c.children&&d(m,u.children)}},U=(c,u,p,y)=>{c==null?n(u.el=f(u.children||""),p,y):u.el=c.el},T=(c,u,p,y)=>{[c.el,c.anchor]=F(c.children,u,p,y,c.el,c.anchor)},I=({el:c,anchor:u},p,y)=>{let m;for(;c&&c!==u;)m=g(c),n(c,p,y),c=m;n(u,p,y)},E=({el:c,anchor:u})=>{let p;for(;c&&c!==u;)p=g(c),i(c),c=p;i(u)},N=(c,u,p,y,m,_,w,x,v)=>{u.type==="svg"?w="svg":u.type==="math"&&(w="mathml"),c==null?ye(u,p,y,m,_,w,x,v):Pt(c,u,m,_,w,x,v)},ye=(c,u,p,y,m,_,w,x)=>{let v,b;const{props:O,shapeFlag:S,transition:A,dirs:R}=c;if(v=c.el=o(c.type,_,O&&O.is,O),S&8?a(v,c.children):S&16&&Ne(c.children,v,null,y,m,gs(c,_),w,x),R&&Ye(c,null,y,"created"),be(v,c,c.scopeId,w,y),O){for(const V in O)V!=="value"&&!gt(V)&&r(v,V,null,O[V],_,y);"value"in O&&r(v,"value",null,O.value,_),(b=O.onVnodeBeforeMount)&&Ce(b,y,c)}R&&Ye(c,null,y,"beforeMount");const D=oo(m,A);D&&A.beforeEnter(v),n(v,u,p),((b=O&&O.onVnodeMounted)||D||R)&&de(()=>{b&&Ce(b,y,c),D&&A.enter(v),R&&Ye(c,null,y,"mounted")},m)},be=(c,u,p,y,m)=>{if(p&&C(c,p),y)for(let _=0;_<y.length;_++)C(c,y[_]);if(m){let _=m.subTree;if(u===_||Ei(_.type)&&(_.ssContent===u||_.ssFallback===u)){const w=m.vnode;be(c,w,w.scopeId,w.slotScopeIds,m.parent)}}},Ne=(c,u,p,y,m,_,w,x,v=0)=>{for(let b=v;b<c.length;b++){const O=c[b]=x?Le(c[b]):Ee(c[b]);$(null,O,u,p,y,m,_,w,x)}},Pt=(c,u,p,y,m,_,w)=>{const x=u.el=c.el;let{patchFlag:v,dynamicChildren:b,dirs:O}=u;v|=c.patchFlag&16;const S=c.props||G,A=u.props||G;let R;if(p&&ze(p,!1),(R=A.onVnodeBeforeUpdate)&&Ce(R,p,u,c),O&&Ye(u,c,p,"beforeUpdate"),p&&ze(p,!0),(S.innerHTML&&A.innerHTML==null||S.textContent&&A.textContent==null)&&a(x,""),b?ke(c.dynamicChildren,b,x,p,y,gs(u,m),_):w||K(c,u,x,null,p,y,gs(u,m),_,!1),v>0){if(v&16)ct(x,S,A,p,m);else if(v&2&&S.class!==A.class&&r(x,"class",null,A.class,m),v&4&&r(x,"style",S.style,A.style,m),v&8){const D=u.dynamicProps;for(let V=0;V<D.length;V++){const B=D[V],ue=S[B],le=A[B];(le!==ue||B==="value")&&r(x,B,ue,le,m,p)}}v&1&&c.children!==u.children&&a(x,u.children)}else!w&&b==null&&ct(x,S,A,p,m);((R=A.onVnodeUpdated)||O)&&de(()=>{R&&Ce(R,p,u,c),O&&Ye(u,c,p,"updated")},y)},ke=(c,u,p,y,m,_,w)=>{for(let x=0;x<u.length;x++){const v=c[x],b=u[x],O=v.el&&(v.type===pe||!ht(v,b)||v.shapeFlag&198)?h(v.el):p;$(v,b,O,null,y,m,_,w,!0)}},ct=(c,u,p,y,m)=>{if(u!==p){if(u!==G)for(const _ in u)!gt(_)&&!(_ in p)&&r(c,_,u[_],null,m,y);for(const _ in p){if(gt(_))continue;const w=p[_],x=u[_];w!==x&&_!=="value"&&r(c,_,x,w,m,y)}"value"in p&&r(c,"value",u.value,p.value,m)}},Rt=(c,u,p,y,m,_,w,x,v)=>{const b=u.el=c?c.el:l(""),O=u.anchor=c?c.anchor:l("");let{patchFlag:S,dynamicChildren:A,slotScopeIds:R}=u;R&&(x=x?x.concat(R):R),c==null?(n(b,p,y),n(O,p,y),Ne(u.children||[],p,O,m,_,w,x,v)):S>0&&S&64&&A&&c.dynamicChildren?(ke(c.dynamicChildren,A,p,m,_,w,x),(u.key!=null||m&&u===m.subTree)&&xi(c,u,!0)):K(c,u,p,O,m,_,w,x,v)},Mt=(c,u,p,y,m,_,w,x,v)=>{u.slotScopeIds=x,c==null?u.shapeFlag&512?m.ctx.activate(u,p,y,w,v):os(u,p,y,m,_,w,v):Xs(c,u,v)},os=(c,u,p,y,m,_,w)=>{const x=c.component=Eo(c,y,m);if(li(c)&&(x.ctx.renderer=ut),Ao(x,!1,w),x.asyncDep){if(m&&m.registerDep(x,se,w),!c.el){const v=x.subTree=me(We);U(null,v,u,p)}}else se(x,c,u,p,m,_,w)},Xs=(c,u,p)=>{const y=u.component=c.component;if(mo(c,u,p))if(y.asyncDep&&!y.asyncResolved){k(y,u,p);return}else y.next=u,y.update();else u.el=c.el,y.vnode=u},se=(c,u,p,y,m,_,w)=>{const x=()=>{if(c.isMounted){let{next:S,bu:A,u:R,parent:D,vnode:V}=c;{const we=wi(c);if(we){S&&(S.el=V.el,k(c,S,w)),we.asyncDep.then(()=>{c.isUnmounted||x()});return}}let B=S,ue;ze(c,!1),S?(S.el=V.el,k(c,S,w)):S=V,A&&fs(A),(ue=S.props&&S.props.onVnodeBeforeUpdate)&&Ce(ue,D,S,V),ze(c,!0);const le=hn(c),xe=c.subTree;c.subTree=le,$(xe,le,h(xe.el),jt(xe),c,m,_),S.el=le.el,B===null&&_o(c,le.el),R&&de(R,m),(ue=S.props&&S.props.onVnodeUpdated)&&de(()=>Ce(ue,D,S,V),m)}else{let S;const{el:A,props:R}=u,{bm:D,m:V,parent:B,root:ue,type:le}=c,xe=yt(u);ze(c,!1),D&&fs(D),!xe&&(S=R&&R.onVnodeBeforeMount)&&Ce(S,B,u),ze(c,!0);{ue.ce&&ue.ce._injectChildStyle(le);const we=c.subTree=hn(c);$(null,we,p,y,c,m,_),u.el=we.el}if(V&&de(V,m),!xe&&(S=R&&R.onVnodeMounted)){const we=u;de(()=>Ce(S,B,we),m)}(u.shapeFlag&256||B&&yt(B.vnode)&&B.vnode.shapeFlag&256)&&c.a&&de(c.a,m),c.isMounted=!0,u=p=y=null}};c.scope.on();const v=c.effect=new Ln(x);c.scope.off();const b=c.update=v.run.bind(v),O=c.job=v.runIfDirty.bind(v);O.i=c,O.id=c.uid,v.scheduler=()=>Vs(O),ze(c,!0),b()},k=(c,u,p)=>{u.component=c;const y=c.vnode.props;c.vnode=u,c.next=null,Qr(c,u.props,y,p),no(c,u.children,p),Fe(),on(c),$e()},K=(c,u,p,y,m,_,w,x,v=!1)=>{const b=c&&c.children,O=c?c.shapeFlag:0,S=u.children,{patchFlag:A,shapeFlag:R}=u;if(A>0){if(A&128){It(b,S,p,y,m,_,w,x,v);return}else if(A&256){Je(b,S,p,y,m,_,w,x,v);return}}R&8?(O&16&&ft(b,m,_),S!==b&&a(p,S)):O&16?R&16?It(b,S,p,y,m,_,w,x,v):ft(b,m,_,!0):(O&8&&a(p,""),R&16&&Ne(S,p,y,m,_,w,x,v))},Je=(c,u,p,y,m,_,w,x,v)=>{c=c||st,u=u||st;const b=c.length,O=u.length,S=Math.min(b,O);let A;for(A=0;A<S;A++){const R=u[A]=v?Le(u[A]):Ee(u[A]);$(c[A],R,p,null,m,_,w,x,v)}b>O?ft(c,m,_,!0,!1,S):Ne(u,p,y,m,_,w,x,v,S)},It=(c,u,p,y,m,_,w,x,v)=>{let b=0;const O=u.length;let S=c.length-1,A=O-1;for(;b<=S&&b<=A;){const R=c[b],D=u[b]=v?Le(u[b]):Ee(u[b]);if(ht(R,D))$(R,D,p,null,m,_,w,x,v);else break;b++}for(;b<=S&&b<=A;){const R=c[S],D=u[A]=v?Le(u[A]):Ee(u[A]);if(ht(R,D))$(R,D,p,null,m,_,w,x,v);else break;S--,A--}if(b>S){if(b<=A){const R=A+1,D=R<O?u[R].el:y;for(;b<=A;)$(null,u[b]=v?Le(u[b]):Ee(u[b]),p,D,m,_,w,x,v),b++}}else if(b>A)for(;b<=S;)ve(c[b],m,_,!0),b++;else{const R=b,D=b,V=new Map;for(b=D;b<=A;b++){const ae=u[b]=v?Le(u[b]):Ee(u[b]);ae.key!=null&&V.set(ae.key,b)}let B,ue=0;const le=A-D+1;let xe=!1,we=0;const at=new Array(le);for(b=0;b<le;b++)at[b]=0;for(b=R;b<=S;b++){const ae=c[b];if(ue>=le){ve(ae,m,_,!0);continue}let Se;if(ae.key!=null)Se=V.get(ae.key);else for(B=D;B<=A;B++)if(at[B-D]===0&&ht(ae,u[B])){Se=B;break}Se===void 0?ve(ae,m,_,!0):(at[Se-D]=b+1,Se>=we?we=Se:xe=!0,$(ae,u[Se],p,null,m,_,w,x,v),ue++)}const en=xe?lo(at):st;for(B=en.length-1,b=le-1;b>=0;b--){const ae=D+b,Se=u[ae],tn=ae+1<O?u[ae+1].el:y;at[b]===0?$(null,Se,p,tn,m,_,w,x,v):xe&&(B<0||b!==en[B]?qe(Se,p,tn,2):B--)}}},qe=(c,u,p,y,m=null)=>{const{el:_,type:w,transition:x,children:v,shapeFlag:b}=c;if(b&6){qe(c.component.subTree,u,p,y);return}if(b&128){c.suspense.move(u,p,y);return}if(b&64){w.move(c,u,p,ut);return}if(w===pe){n(_,u,p);for(let S=0;S<v.length;S++)qe(v[S],u,p,y);n(c.anchor,u,p);return}if(w===_s){I(c,u,p);return}if(y!==2&&b&1&&x)if(y===0)x.beforeEnter(_),n(_,u,p),de(()=>x.enter(_),m);else{const{leave:S,delayLeave:A,afterLeave:R}=x,D=()=>{c.ctx.isUnmounted?i(_):n(_,u,p)},V=()=>{S(_,()=>{D(),R&&R()})};A?A(_,D,V):V()}else n(_,u,p)},ve=(c,u,p,y=!1,m=!1)=>{const{type:_,props:w,ref:x,children:v,dynamicChildren:b,shapeFlag:O,patchFlag:S,dirs:A,cacheIndex:R}=c;if(S===-2&&(m=!1),x!=null&&(Fe(),Wt(x,null,p,c,!0),$e()),R!=null&&(u.renderCache[R]=void 0),O&256){u.ctx.deactivate(c);return}const D=O&1&&A,V=!yt(c);let B;if(V&&(B=w&&w.onVnodeBeforeUnmount)&&Ce(B,u,c),O&6)Di(c.component,p,y);else{if(O&128){c.suspense.unmount(p,y);return}D&&Ye(c,null,u,"beforeUnmount"),O&64?c.type.remove(c,u,p,ut,y):b&&!b.hasOnce&&(_!==pe||S>0&&S&64)?ft(b,u,p,!1,!0):(_===pe&&S&384||!m&&O&16)&&ft(v,u,p),y&&Zs(c)}(V&&(B=w&&w.onVnodeUnmounted)||D)&&de(()=>{B&&Ce(B,u,c),D&&Ye(c,null,u,"unmounted")},p)},Zs=c=>{const{type:u,el:p,anchor:y,transition:m}=c;if(u===pe){$i(p,y);return}if(u===_s){E(c);return}const _=()=>{i(p),m&&!m.persisted&&m.afterLeave&&m.afterLeave()};if(c.shapeFlag&1&&m&&!m.persisted){const{leave:w,delayLeave:x}=m,v=()=>w(p,_);x?x(c.el,_,v):v()}else _()},$i=(c,u)=>{let p;for(;c!==u;)p=g(c),i(c),c=p;i(u)},Di=(c,u,p)=>{const{bum:y,scope:m,job:_,subTree:w,um:x,m:v,a:b,parent:O,slots:{__:S}}=c;dn(v),dn(b),y&&fs(y),O&&M(S)&&S.forEach(A=>{O.renderCache[A]=void 0}),m.stop(),_&&(_.flags|=8,ve(w,c,u,p)),x&&de(x,u),de(()=>{c.isUnmounted=!0},u),u&&u.pendingBranch&&!u.isUnmounted&&c.asyncDep&&!c.asyncResolved&&c.suspenseId===u.pendingId&&(u.deps--,u.deps===0&&u.resolve())},ft=(c,u,p,y=!1,m=!1,_=0)=>{for(let w=_;w<c.length;w++)ve(c[w],u,p,y,m)},jt=c=>{if(c.shapeFlag&6)return jt(c.component.subTree);if(c.shapeFlag&128)return c.suspense.next();const u=g(c.anchor||c.el),p=u&&u[Pr];return p?g(p):u};let ls=!1;const Qs=(c,u,p)=>{c==null?u._vnode&&ve(u._vnode,null,null,!0):$(u._vnode||null,c,u,null,null,null,p),u._vnode=c,ls||(ls=!0,on(),ni(),ls=!1)},ut={p:$,um:ve,m:qe,r:Zs,mt:os,mc:Ne,pc:K,pbc:ke,n:jt,o:e};return{render:Qs,hydrate:void 0,createApp:zr(Qs)}}function gs({type:e,props:t},s){return s==="svg"&&e==="foreignObject"||s==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:s}function ze({effect:e,job:t},s){s?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function oo(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function xi(e,t,s=!1){const n=e.children,i=t.children;if(M(n)&&M(i))for(let r=0;r<n.length;r++){const o=n[r];let l=i[r];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=i[r]=Le(i[r]),l.el=o.el),!s&&l.patchFlag!==-2&&xi(o,l)),l.type===is&&(l.el=o.el),l.type===We&&!l.el&&(l.el=o.el)}}function lo(e){const t=e.slice(),s=[0];let n,i,r,o,l;const f=e.length;for(n=0;n<f;n++){const d=e[n];if(d!==0){if(i=s[s.length-1],e[i]<d){t[n]=i,s.push(n);continue}for(r=0,o=s.length-1;r<o;)l=r+o>>1,e[s[l]]<d?r=l+1:o=l;d<e[s[r]]&&(r>0&&(t[n]=s[r-1]),s[r]=n)}}for(r=s.length,o=s[r-1];r-- >0;)s[r]=o,o=t[o];return s}function wi(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:wi(t)}function dn(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const co=Symbol.for("v-scx"),fo=()=>Nt(co);function ms(e,t,s){return Si(e,t,s)}function Si(e,t,s=G){const{immediate:n,deep:i,flush:r,once:o}=s,l=te({},s),f=t&&n||!t&&r!=="post";let d;if(Tt){if(r==="sync"){const C=fo();d=C.__watcherHandles||(C.__watcherHandles=[])}else if(!f){const C=()=>{};return C.stop=Oe,C.resume=Oe,C.pause=Oe,C}}const a=oe;l.call=(C,F,$)=>Pe(C,a,F,$);let h=!1;r==="post"?l.scheduler=C=>{de(C,a&&a.suspense)}:r!=="sync"&&(h=!0,l.scheduler=(C,F)=>{F?C():Vs(C)}),l.augmentJob=C=>{t&&(C.flags|=4),h&&(C.flags|=2,a&&(C.id=a.uid,C.i=a))};const g=Sr(e,t,l);return Tt&&(d?d.push(g):f&&g()),g}function uo(e,t,s){const n=this.proxy,i=q(e)?e.includes(".")?Ci(n,e):()=>n[e]:e.bind(n,n);let r;j(t)?r=t:(r=t.handler,s=t);const o=Ot(this),l=Si(i,r.bind(n),s);return o(),l}function Ci(e,t){const s=t.split(".");return()=>{let n=e;for(let i=0;i<s.length&&n;i++)n=n[s[i]];return n}}const ao=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Ke(t)}Modifiers`]||e[`${Qe(t)}Modifiers`];function ho(e,t,...s){if(e.isUnmounted)return;const n=e.vnode.props||G;let i=s;const r=t.startsWith("update:"),o=r&&ao(n,t.slice(7));o&&(o.trim&&(i=s.map(a=>q(a)?a.trim():a)),o.number&&(i=s.map(Ki)));let l,f=n[l=cs(t)]||n[l=cs(Ke(t))];!f&&r&&(f=n[l=cs(Qe(t))]),f&&Pe(f,e,6,i);const d=n[l+"Once"];if(d){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,Pe(d,e,6,i)}}function Ti(e,t,s=!1){const n=t.emitsCache,i=n.get(e);if(i!==void 0)return i;const r=e.emits;let o={},l=!1;if(!j(e)){const f=d=>{const a=Ti(d,t,!0);a&&(l=!0,te(o,a))};!s&&t.mixins.length&&t.mixins.forEach(f),e.extends&&f(e.extends),e.mixins&&e.mixins.forEach(f)}return!r&&!l?(J(e)&&n.set(e,null),null):(M(r)?r.forEach(f=>o[f]=null):te(o,r),J(e)&&n.set(e,o),o)}function ns(e,t){return!e||!qt(t)?!1:(t=t.slice(2).replace(/Once$/,""),L(e,t[0].toLowerCase()+t.slice(1))||L(e,Qe(t))||L(e,t))}function hn(e){const{type:t,vnode:s,proxy:n,withProxy:i,propsOptions:[r],slots:o,attrs:l,emit:f,render:d,renderCache:a,props:h,data:g,setupState:C,ctx:F,inheritAttrs:$}=e,Y=Vt(e);let U,T;try{if(s.shapeFlag&4){const E=i||n,N=E;U=Ee(d.call(N,E,a,h,C,g,F)),T=l}else{const E=t;U=Ee(E.length>1?E(h,{attrs:l,slots:o,emit:f}):E(h,null)),T=t.props?l:po(l)}}catch(E){vt.length=0,es(E,e,1),U=me(We)}let I=U;if(T&&$!==!1){const E=Object.keys(T),{shapeFlag:N}=I;E.length&&N&7&&(r&&E.some(Is)&&(T=go(T,r)),I=lt(I,T,!1,!0))}return s.dirs&&(I=lt(I,null,!1,!0),I.dirs=I.dirs?I.dirs.concat(s.dirs):s.dirs),s.transition&&Ws(I,s.transition),U=I,Vt(Y),U}const po=e=>{let t;for(const s in e)(s==="class"||s==="style"||qt(s))&&((t||(t={}))[s]=e[s]);return t},go=(e,t)=>{const s={};for(const n in e)(!Is(n)||!(n.slice(9)in t))&&(s[n]=e[n]);return s};function mo(e,t,s){const{props:n,children:i,component:r}=e,{props:o,children:l,patchFlag:f}=t,d=r.emitsOptions;if(t.dirs||t.transition)return!0;if(s&&f>=0){if(f&1024)return!0;if(f&16)return n?pn(n,o,d):!!o;if(f&8){const a=t.dynamicProps;for(let h=0;h<a.length;h++){const g=a[h];if(o[g]!==n[g]&&!ns(d,g))return!0}}}else return(i||l)&&(!l||!l.$stable)?!0:n===o?!1:n?o?pn(n,o,d):!0:!!o;return!1}function pn(e,t,s){const n=Object.keys(t);if(n.length!==Object.keys(e).length)return!0;for(let i=0;i<n.length;i++){const r=n[i];if(t[r]!==e[r]&&!ns(s,r))return!0}return!1}function _o({vnode:e,parent:t},s){for(;t;){const n=t.subTree;if(n.suspense&&n.suspense.activeBranch===e&&(n.el=e.el),n===e)(e=t.vnode).el=s,t=t.parent;else break}}const Ei=e=>e.__isSuspense;function yo(e,t){t&&t.pendingBranch?M(e)?t.effects.push(...e):t.effects.push(e):Ar(e)}const pe=Symbol.for("v-fgt"),is=Symbol.for("v-txt"),We=Symbol.for("v-cmt"),_s=Symbol.for("v-stc"),vt=[];let he=null;function re(e=!1){vt.push(he=e?null:[])}function bo(){vt.pop(),he=vt[vt.length-1]||null}let Ct=1;function gn(e,t=!1){Ct+=e,e<0&&he&&t&&(he.hasOnce=!0)}function Ai(e){return e.dynamicChildren=Ct>0?he||st:null,bo(),Ct>0&&he&&he.push(e),e}function fe(e,t,s,n,i,r){return Ai(P(e,t,s,n,i,r,!0))}function vo(e,t,s,n,i){return Ai(me(e,t,s,n,i,!0))}function Oi(e){return e?e.__v_isVNode===!0:!1}function ht(e,t){return e.type===t.type&&e.key===t.key}const Pi=({key:e})=>e??null,Ht=({ref:e,ref_key:t,ref_for:s})=>(typeof e=="number"&&(e=""+e),e!=null?q(e)||ee(e)||j(e)?{i:Ae,r:e,k:t,f:!!s}:e:null);function P(e,t=null,s=null,n=0,i=null,r=e===pe?0:1,o=!1,l=!1){const f={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Pi(t),ref:t&&Ht(t),scopeId:ri,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:r,patchFlag:n,dynamicProps:i,dynamicChildren:null,appContext:null,ctx:Ae};return l?(Ys(f,s),r&128&&e.normalize(f)):s&&(f.shapeFlag|=q(s)?8:16),Ct>0&&!o&&he&&(f.patchFlag>0||r&6)&&f.patchFlag!==32&&he.push(f),f}const me=xo;function xo(e,t=null,s=null,n=0,i=null,r=!1){if((!e||e===Kr)&&(e=We),Oi(e)){const l=lt(e,t,!0);return s&&Ys(l,s),Ct>0&&!r&&he&&(l.shapeFlag&6?he[he.indexOf(e)]=l:he.push(l)),l.patchFlag=-2,l}if(Mo(e)&&(e=e.__vccOpts),t){t=wo(t);let{class:l,style:f}=t;l&&!q(l)&&(t.class=Et(l)),J(f)&&(Ks(f)&&!M(f)&&(f=te({},f)),t.style=Zt(f))}const o=q(e)?1:Ei(e)?128:Rr(e)?64:J(e)?4:j(e)?2:0;return P(e,t,s,n,i,o,r,!0)}function wo(e){return e?Ks(e)||gi(e)?te({},e):e:null}function lt(e,t,s=!1,n=!1){const{props:i,ref:r,patchFlag:o,children:l,transition:f}=e,d=t?So(i||{},t):i,a={__v_isVNode:!0,__v_skip:!0,type:e.type,props:d,key:d&&Pi(d),ref:t&&t.ref?s&&r?M(r)?r.concat(Ht(t)):[r,Ht(t)]:Ht(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==pe?o===-1?16:o|16:o,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:f,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&lt(e.ssContent),ssFallback:e.ssFallback&&lt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return f&&n&&Ws(a,f.clone(a)),a}function Ri(e=" ",t=0){return me(is,null,e,t)}function kt(e="",t=!1){return t?(re(),vo(We,null,e)):me(We,null,e)}function Ee(e){return e==null||typeof e=="boolean"?me(We):M(e)?me(pe,null,e.slice()):Oi(e)?Le(e):me(is,null,String(e))}function Le(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:lt(e)}function Ys(e,t){let s=0;const{shapeFlag:n}=e;if(t==null)t=null;else if(M(t))s=16;else if(typeof t=="object")if(n&65){const i=t.default;i&&(i._c&&(i._d=!1),Ys(e,i()),i._c&&(i._d=!0));return}else{s=32;const i=t._;!i&&!gi(t)?t._ctx=Ae:i===3&&Ae&&(Ae.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else j(t)?(t={default:t,_ctx:Ae},s=32):(t=String(t),n&64?(s=16,t=[Ri(t)]):s=8);e.children=t,e.shapeFlag|=s}function So(...e){const t={};for(let s=0;s<e.length;s++){const n=e[s];for(const i in n)if(i==="class")t.class!==n.class&&(t.class=Et([t.class,n.class]));else if(i==="style")t.style=Zt([t.style,n.style]);else if(qt(i)){const r=t[i],o=n[i];o&&r!==o&&!(M(r)&&r.includes(o))&&(t[i]=r?[].concat(r,o):o)}else i!==""&&(t[i]=n[i])}return t}function Ce(e,t,s,n=null){Pe(e,t,7,[s,n])}const Co=di();let To=0;function Eo(e,t,s){const n=e.type,i=(t?t.appContext:e.appContext)||Co,r={uid:To++,vnode:e,type:n,parent:t,appContext:i,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Yi(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(i.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:_i(n,i),emitsOptions:Ti(n,i),emit:null,emitted:null,propsDefaults:G,inheritAttrs:n.inheritAttrs,ctx:G,data:G,props:G,attrs:G,slots:G,refs:G,setupState:G,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return r.ctx={_:r},r.root=t?t.root:r,r.emit=ho.bind(null,r),e.ce&&e.ce(r),r}let oe=null,Jt,Ps;{const e=Xt(),t=(s,n)=>{let i;return(i=e[s])||(i=e[s]=[]),i.push(n),r=>{i.length>1?i.forEach(o=>o(r)):i[0](r)}};Jt=t("__VUE_INSTANCE_SETTERS__",s=>oe=s),Ps=t("__VUE_SSR_SETTERS__",s=>Tt=s)}const Ot=e=>{const t=oe;return Jt(e),e.scope.on(),()=>{e.scope.off(),Jt(t)}},mn=()=>{oe&&oe.scope.off(),Jt(null)};function Mi(e){return e.vnode.shapeFlag&4}let Tt=!1;function Ao(e,t=!1,s=!1){t&&Ps(t);const{props:n,children:i}=e.vnode,r=Mi(e);Zr(e,n,r,t),so(e,i,s||t);const o=r?Oo(e,t):void 0;return t&&Ps(!1),o}function Oo(e,t){const s=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Vr);const{setup:n}=s;if(n){Fe();const i=e.setupContext=n.length>1?Ro(e):null,r=Ot(e),o=At(n,e,0,[e.props,i]),l=Mn(o);if($e(),r(),(l||e.sp)&&!yt(e)&&oi(e),l){if(o.then(mn,mn),t)return o.then(f=>{_n(e,f)}).catch(f=>{es(f,e,0)});e.asyncDep=o}else _n(e,o)}else Ii(e)}function _n(e,t,s){j(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:J(t)&&(e.setupState=ei(t)),Ii(e)}function Ii(e,t,s){const n=e.type;e.render||(e.render=n.render||Oe);{const i=Ot(e);Fe();try{Wr(e)}finally{$e(),i()}}}const Po={get(e,t){return Q(e,"get",""),e[t]}};function Ro(e){const t=s=>{e.exposed=s||{}};return{attrs:new Proxy(e.attrs,Po),slots:e.slots,emit:e.emit,expose:t}}function zs(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(ei(mr(e.exposed)),{get(t,s){if(s in t)return t[s];if(s in bt)return bt[s](e)},has(t,s){return s in t||s in bt}})):e.proxy}function Mo(e){return j(e)&&"__vccOpts"in e}const ji=(e,t)=>xr(e,t,Tt),Io="3.5.16";/**
* @vue/runtime-dom v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Rs;const yn=typeof window<"u"&&window.trustedTypes;if(yn)try{Rs=yn.createPolicy("vue",{createHTML:e=>e})}catch{}const Fi=Rs?e=>Rs.createHTML(e):e=>e,jo="http://www.w3.org/2000/svg",Fo="http://www.w3.org/1998/Math/MathML",Me=typeof document<"u"?document:null,bn=Me&&Me.createElement("template"),$o={insert:(e,t,s)=>{t.insertBefore(e,s||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,s,n)=>{const i=t==="svg"?Me.createElementNS(jo,e):t==="mathml"?Me.createElementNS(Fo,e):s?Me.createElement(e,{is:s}):Me.createElement(e);return e==="select"&&n&&n.multiple!=null&&i.setAttribute("multiple",n.multiple),i},createText:e=>Me.createTextNode(e),createComment:e=>Me.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Me.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,s,n,i,r){const o=s?s.previousSibling:t.lastChild;if(i&&(i===r||i.nextSibling))for(;t.insertBefore(i.cloneNode(!0),s),!(i===r||!(i=i.nextSibling)););else{bn.innerHTML=Fi(n==="svg"?`<svg>${e}</svg>`:n==="mathml"?`<math>${e}</math>`:e);const l=bn.content;if(n==="svg"||n==="mathml"){const f=l.firstChild;for(;f.firstChild;)l.appendChild(f.firstChild);l.removeChild(f)}t.insertBefore(l,s)}return[o?o.nextSibling:t.firstChild,s?s.previousSibling:t.lastChild]}},Do=Symbol("_vtc");function No(e,t,s){const n=e[Do];n&&(t=(t?[t,...n]:[...n]).join(" ")),t==null?e.removeAttribute("class"):s?e.setAttribute("class",t):e.className=t}const vn=Symbol("_vod"),Ho=Symbol("_vsh"),Lo=Symbol(""),Uo=/(^|;)\s*display\s*:/;function Bo(e,t,s){const n=e.style,i=q(s);let r=!1;if(s&&!i){if(t)if(q(t))for(const o of t.split(";")){const l=o.slice(0,o.indexOf(":")).trim();s[l]==null&&Lt(n,l,"")}else for(const o in t)s[o]==null&&Lt(n,o,"");for(const o in s)o==="display"&&(r=!0),Lt(n,o,s[o])}else if(i){if(t!==s){const o=n[Lo];o&&(s+=";"+o),n.cssText=s,r=Uo.test(s)}}else t&&e.removeAttribute("style");vn in e&&(e[vn]=r?n.display:"",e[Ho]&&(n.display="none"))}const xn=/\s*!important$/;function Lt(e,t,s){if(M(s))s.forEach(n=>Lt(e,t,n));else if(s==null&&(s=""),t.startsWith("--"))e.setProperty(t,s);else{const n=Ko(e,t);xn.test(s)?e.setProperty(Qe(n),s.replace(xn,""),"important"):e[n]=s}}const wn=["Webkit","Moz","ms"],ys={};function Ko(e,t){const s=ys[t];if(s)return s;let n=Ke(t);if(n!=="filter"&&n in e)return ys[t]=n;n=Fn(n);for(let i=0;i<wn.length;i++){const r=wn[i]+n;if(r in e)return ys[t]=r}return t}const Sn="http://www.w3.org/1999/xlink";function Cn(e,t,s,n,i,r=qi(t)){n&&t.startsWith("xlink:")?s==null?e.removeAttributeNS(Sn,t.slice(6,t.length)):e.setAttributeNS(Sn,t,s):s==null||r&&!Dn(s)?e.removeAttribute(t):e.setAttribute(t,r?"":Ge(s)?String(s):s)}function Tn(e,t,s,n,i){if(t==="innerHTML"||t==="textContent"){s!=null&&(e[t]=t==="innerHTML"?Fi(s):s);return}const r=e.tagName;if(t==="value"&&r!=="PROGRESS"&&!r.includes("-")){const l=r==="OPTION"?e.getAttribute("value")||"":e.value,f=s==null?e.type==="checkbox"?"on":"":String(s);(l!==f||!("_value"in e))&&(e.value=f),s==null&&e.removeAttribute(t),e._value=s;return}let o=!1;if(s===""||s==null){const l=typeof e[t];l==="boolean"?s=Dn(s):s==null&&l==="string"?(s="",o=!0):l==="number"&&(s=0,o=!0)}try{e[t]=s}catch{}o&&e.removeAttribute(i||t)}function Vo(e,t,s,n){e.addEventListener(t,s,n)}function Wo(e,t,s,n){e.removeEventListener(t,s,n)}const En=Symbol("_vei");function Go(e,t,s,n,i=null){const r=e[En]||(e[En]={}),o=r[t];if(n&&o)o.value=n;else{const[l,f]=ko(t);if(n){const d=r[t]=Yo(n,i);Vo(e,l,d,f)}else o&&(Wo(e,l,o,f),r[t]=void 0)}}const An=/(?:Once|Passive|Capture)$/;function ko(e){let t;if(An.test(e)){t={};let n;for(;n=e.match(An);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Qe(e.slice(2)),t]}let bs=0;const Jo=Promise.resolve(),qo=()=>bs||(Jo.then(()=>bs=0),bs=Date.now());function Yo(e,t){const s=n=>{if(!n._vts)n._vts=Date.now();else if(n._vts<=s.attached)return;Pe(zo(n,s.value),t,5,[n])};return s.value=e,s.attached=qo(),s}function zo(e,t){if(M(t)){const s=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{s.call(e),e._stopped=!0},t.map(n=>i=>!i._stopped&&n&&n(i))}else return t}const On=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Xo=(e,t,s,n,i,r)=>{const o=i==="svg";t==="class"?No(e,n,o):t==="style"?Bo(e,s,n):qt(t)?Is(t)||Go(e,t,s,n,r):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Zo(e,t,n,o))?(Tn(e,t,n),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Cn(e,t,n,o,r,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!q(n))?Tn(e,Ke(t),n,r,t):(t==="true-value"?e._trueValue=n:t==="false-value"&&(e._falseValue=n),Cn(e,t,n,o))};function Zo(e,t,s,n){if(n)return!!(t==="innerHTML"||t==="textContent"||t in e&&On(t)&&j(s));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const i=e.tagName;if(i==="IMG"||i==="VIDEO"||i==="CANVAS"||i==="SOURCE")return!1}return On(t)&&q(s)?!1:t in e}const Qo=["ctrl","shift","alt","meta"],el={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Qo.some(s=>e[`${s}Key`]&&!t.includes(s))},tl=(e,t)=>{const s=e._withMods||(e._withMods={}),n=t.join(".");return s[n]||(s[n]=(i,...r)=>{for(let o=0;o<t.length;o++){const l=el[t[o]];if(l&&l(i,t))return}return e(i,...r)})},sl=te({patchProp:Xo},$o);let Pn;function nl(){return Pn||(Pn=io(sl))}const il=(...e)=>{const t=nl().createApp(...e),{mount:s}=t;return t.mount=n=>{const i=ol(n);if(!i)return;const r=t._component;!j(r)&&!r.render&&!r.template&&(r.template=i.innerHTML),i.nodeType===1&&(i.textContent="");const o=s(i,!1,rl(i));return i instanceof Element&&(i.removeAttribute("v-cloak"),i.setAttribute("data-v-app","")),o},t};function rl(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function ol(e){return q(e)?document.querySelector(e):e}const vs="immortal-jelly-game";function ll(){const e=Ie(0),t=Ie(0),s=Ie(0),n=Ie(1),i=Ie([]),r=Ie([{id:"plankton",name:"Plankton Colony",description:"Microscopic organisms that feed your jellyfish",cost:15,baseCost:15,owned:0,effect:.1,type:"auto"},{id:"polyp",name:"Polyp Garden",description:"Juvenile jellyfish that grow into adults",cost:100,baseCost:100,owned:0,effect:1,type:"auto"},{id:"coral_reef",name:"Coral Reef",description:"A thriving ecosystem that nurtures jellyfish",cost:1100,baseCost:1100,owned:0,effect:8,type:"auto"},{id:"deep_trench",name:"Deep Sea Trench",description:"Ancient depths where immortal jellyfish multiply",cost:12e3,baseCost:12e3,owned:0,effect:47,type:"auto"},{id:"bio_lab",name:"Marine Biology Lab",description:"Scientific facility that cultivates immortal cells",cost:13e4,baseCost:13e4,owned:0,effect:260,type:"auto"},{id:"click_upgrade",name:"Bioluminescent Touch",description:"Your touch stimulates cellular regeneration",cost:100,baseCost:100,owned:0,effect:2,type:"click"},{id:"multiplier",name:"Immortality Gene",description:"Doubles all jellyfish reproduction",cost:5e4,baseCost:5e4,owned:0,effect:2,type:"multiplier"}]),o=T=>e.value>=T.cost,l=T=>T<1e3?Math.floor(T).toString():T<1e6?(T/1e3).toFixed(1)+"K":T<1e9?(T/1e6).toFixed(1)+"M":T<1e12?(T/1e9).toFixed(1)+"B":(T/1e12).toFixed(1)+"T",f=()=>{const T=n.value;e.value+=T,t.value+=T,h()},d=T=>{const I=r.value.find(E=>E.id===T);!I||!o(I)||(e.value-=I.cost,I.owned++,I.cost=Math.ceil(I.baseCost*Math.pow(1.15,I.owned)),a(),h(),g())},a=()=>{let T=0,I=1,E=1;r.value.forEach(N=>{N.type==="auto"?T+=N.owned*N.effect:N.type==="click"?I*=Math.pow(N.effect,N.owned):N.type==="multiplier"&&(E*=Math.pow(N.effect,N.owned))}),s.value=T*E,n.value=I*E},h=()=>{const T=[];t.value>=100&&!i.value.includes("first_colony")&&T.push("first_colony"),t.value>=1e3&&!i.value.includes("immortal_swarm")&&T.push("immortal_swarm"),r.value.some(I=>I.owned>=10)&&!i.value.includes("ecosystem_builder")&&T.push("ecosystem_builder"),t.value>=1e4&&!i.value.includes("ocean_master")&&T.push("ocean_master"),i.value.push(...T)},g=()=>{const T={jellyfish:e.value,totalJellyfish:t.value,jellyfishPerSecond:s.value,clickPower:n.value,upgrades:r.value,achievements:i.value,lastSave:Date.now()};localStorage.setItem(vs,JSON.stringify(T))},C=()=>{const T=localStorage.getItem(vs);if(T)try{const I=JSON.parse(T);e.value=I.jellyfish||0,t.value=I.totalJellyfish||0,i.value=I.achievements||[],I.upgrades&&I.upgrades.forEach(E=>{const N=r.value.find(ye=>ye.id===E.id);N&&(N.owned=E.owned,N.cost=E.cost)}),a()}catch(I){console.error("Failed to load game:",I)}},F=()=>{e.value=0,t.value=0,i.value=[],r.value.forEach(T=>{T.owned=0,T.cost=T.baseCost}),a(),localStorage.removeItem(vs)};let $=null;const Y=()=>{$=setInterval(()=>{if(s.value>0){const T=s.value/10;e.value+=T,t.value+=T}Date.now()%3e4<100&&g()},100)},U=()=>{$&&(clearInterval($),$=null)};return fi(()=>{C(),Y()}),Gs(()=>{U(),g()}),{jellyfish:e,totalJellyfish:t,jellyfishPerSecond:s,clickPower:n,upgrades:r,achievements:i,clickJellyfish:f,buyUpgrade:d,saveGame:g,loadGame:C,resetGame:F,canAfford:o,formatNumber:l}}const cl={class:"jellyfish-container"},fl={class:"jellyfish-stats"},ul={key:0},al={class:"jellyfish-wrapper"},dl=ts({__name:"CookieClicker",props:{jellyfish:{},jellyfishPerSecond:{},clickPower:{},formatNumber:{type:Function}},emits:["click"],setup(e,{emit:t}){const s=t,n=Ie(!1),i=Ie([]);let r=0;const o=async l=>{s("click"),n.value=!0,setTimeout(()=>{n.value=!1},100);const f=l.target.getBoundingClientRect(),d={id:r++,x:l.clientX-f.left,y:l.clientY-f.top,opacity:1};i.value.push(d);const a=()=>{const h=i.value.findIndex(g=>g.id===d.id);h!==-1&&(i.value[h].y-=2,i.value[h].opacity-=.02,i.value[h].opacity<=0?i.value.splice(h,1):requestAnimationFrame(a))};requestAnimationFrame(a)};return(l,f)=>(re(),fe("div",cl,[P("div",fl,[P("h2",null,Z(l.formatNumber(l.jellyfish))+" immortal jellyfish",1),l.jellyfishPerSecond>0?(re(),fe("p",ul,Z(l.formatNumber(l.jellyfishPerSecond))+" per second",1)):kt("",!0)]),P("div",al,[P("button",{class:Et(["jellyfish-button",{"jellyfish-clicked":n.value}]),onClick:o}," 🪼 ",2),(re(!0),fe(pe,null,ks(i.value,d=>(re(),fe("div",{key:d.id,class:"click-effect",style:Zt({left:d.x+"px",top:d.y+"px",opacity:d.opacity})}," +"+Z(l.formatNumber(l.clickPower)),5))),128))])]))}}),rs=(e,t)=>{const s=e.__vccOpts||e;for(const[n,i]of t)s[n]=i;return s},hl=rs(dl,[["__scopeId","data-v-25dabb70"]]),pl={class:"upgrade-shop"},gl={class:"upgrades-list"},ml=["onClick"],_l={class:"upgrade-icon"},yl={class:"upgrade-info"},bl={class:"upgrade-header"},vl={key:0,class:"owned-count"},xl={class:"upgrade-description"},wl={class:"upgrade-stats"},Sl={class:"upgrade-effect"},Cl={class:"upgrade-cost"},Tl={class:"cost-amount"},El=ts({__name:"UpgradeShop",props:{upgrades:{},canAfford:{type:Function},formatNumber:{type:Function}},emits:["buyUpgrade"],setup(e,{emit:t}){const s=e,n=t,i=l=>{const f=s.upgrades.find(d=>d.id===l);f&&s.canAfford(f)&&n("buyUpgrade",l)},r=(l,f)=>({plankton:"🦠",polyp:"🌱",coral_reef:"🪸",deep_trench:"🕳️",bio_lab:"🧬",click_upgrade:"✨",multiplier:"🧪"})[l]||(f==="auto"?"🌊":"⚡"),o=l=>{switch(l.type){case"auto":return`+${l.effect} jellyfish/sec`;case"click":return`${l.effect}x regeneration power`;case"multiplier":return`${l.effect}x all reproduction`;default:return""}};return(l,f)=>(re(),fe("div",pl,[f[1]||(f[1]=P("h3",null,"Marine Ecosystem",-1)),P("div",gl,[(re(!0),fe(pe,null,ks(l.upgrades,d=>(re(),fe("div",{key:d.id,class:Et(["upgrade-item",{affordable:l.canAfford(d),owned:d.owned>0}]),onClick:a=>i(d.id)},[P("div",_l,Z(r(d.id,d.type)),1),P("div",yl,[P("div",bl,[P("h4",null,Z(d.name),1),d.owned>0?(re(),fe("span",vl,Z(d.owned),1)):kt("",!0)]),P("p",xl,Z(d.description),1),P("div",wl,[P("span",Sl,Z(o(d)),1)])]),P("div",Cl,[P("span",Tl,Z(l.formatNumber(d.cost)),1),f[0]||(f[0]=P("span",{class:"cost-currency"},"🪼",-1))])],10,ml))),128))])]))}}),Al=rs(El,[["__scopeId","data-v-b39868df"]]),Ol={class:"stats-panel"},Pl={class:"stats-grid"},Rl={class:"stat-item"},Ml={class:"stat-value"},Il={class:"stat-item"},jl={class:"stat-value"},Fl={class:"stat-item"},$l={class:"stat-value"},Dl={class:"stat-item"},Nl={class:"stat-value"},Hl={key:0,class:"achievements-section"},Ll={class:"achievements-list"},Ul=["title"],Bl={class:"controls-section"},Kl={class:"modal-buttons"},Vl=ts({__name:"StatsPanel",props:{totalJellyfish:{},jellyfishPerSecond:{},clickPower:{},upgrades:{},achievements:{},formatNumber:{type:Function}},emits:["saveGame","resetGame"],setup(e,{emit:t}){const s=e,n=t,i=Ie(!1),r=ji(()=>s.upgrades.filter(h=>h.type==="auto").reduce((h,g)=>h+g.owned,0)),o=()=>{n("saveGame")},l=()=>{n("resetGame"),i.value=!1},f=h=>({first_colony:"First Colony",immortal_swarm:"Immortal Swarm",ecosystem_builder:"Ecosystem Builder",ocean_master:"Ocean Master"})[h]||h,d=h=>({first_colony:"Cultivate your first 100 immortal jellyfish",immortal_swarm:"Reach 1,000 total jellyfish",ecosystem_builder:"Own 10 of any ecosystem component",ocean_master:"Achieve 10,000 total jellyfish"})[h]||"",a=h=>({first_colony:"🌊",immortal_swarm:"🪼",ecosystem_builder:"🪸",ocean_master:"🌀"})[h]||"🏆";return(h,g)=>(re(),fe("div",Ol,[g[11]||(g[11]=P("h3",null,"Research Data",-1)),P("div",Pl,[P("div",Rl,[g[4]||(g[4]=P("span",{class:"stat-label"},"Total Jellyfish Cultivated:",-1)),P("span",Ml,Z(h.formatNumber(h.totalJellyfish)),1)]),P("div",Il,[g[5]||(g[5]=P("span",{class:"stat-label"},"Reproduction Rate:",-1)),P("span",jl,Z(h.formatNumber(h.jellyfishPerSecond))+"/sec",1)]),P("div",Fl,[g[6]||(g[6]=P("span",{class:"stat-label"},"Regeneration Power:",-1)),P("span",$l,Z(h.formatNumber(h.clickPower)),1)]),P("div",Dl,[g[7]||(g[7]=P("span",{class:"stat-label"},"Ecosystem Components:",-1)),P("span",Nl,Z(r.value),1)])]),h.achievements.length>0?(re(),fe("div",Hl,[g[8]||(g[8]=P("h4",null,"Achievements",-1)),P("div",Ll,[(re(!0),fe(pe,null,ks(h.achievements,C=>(re(),fe("div",{key:C,class:"achievement-item",title:d(C)},[Ri(Z(a(C))+" ",1),P("span",null,Z(f(C)),1)],8,Ul))),128))])])):kt("",!0),P("div",Bl,[P("button",{onClick:o,class:"control-button save-button"}," 💾 Save Game "),P("button",{onClick:g[0]||(g[0]=C=>i.value=!0),class:"control-button reset-button"}," 🔄 Reset Game ")]),i.value?(re(),fe("div",{key:1,class:"modal-overlay",onClick:g[3]||(g[3]=C=>i.value=!1)},[P("div",{class:"modal-content",onClick:g[2]||(g[2]=tl(()=>{},["stop"]))},[g[9]||(g[9]=P("h4",null,"Reset Game?",-1)),g[10]||(g[10]=P("p",null,"This will permanently delete all your progress. Are you sure?",-1)),P("div",Kl,[P("button",{onClick:g[1]||(g[1]=C=>i.value=!1),class:"cancel-button"},"Cancel"),P("button",{onClick:l,class:"confirm-button"},"Reset")])])])):kt("",!0)]))}}),Wl=rs(Vl,[["__scopeId","data-v-f4857ae7"]]),Gl={class:"app"},kl={class:"app-main"},Jl={class:"game-area"},ql={class:"sidebar"},Yl=ts({__name:"App",setup(e){const{jellyfish:t,totalJellyfish:s,jellyfishPerSecond:n,clickPower:i,upgrades:r,achievements:o,clickJellyfish:l,buyUpgrade:f,saveGame:d,resetGame:a,canAfford:h,formatNumber:g}=ll();return(C,F)=>(re(),fe("div",Gl,[F[0]||(F[0]=P("header",{class:"app-header"},[P("h1",null,"🪼 Immortal Jelly Research Lab 🧬"),P("p",{class:"subtitle"},"Study the immortal jellyfish and unlock the secrets of eternal life!")],-1)),P("main",kl,[P("div",Jl,[me(hl,{jellyfish:z(t),"jellyfish-per-second":z(n),"click-power":z(i),"format-number":z(g),onClick:z(l)},null,8,["jellyfish","jellyfish-per-second","click-power","format-number","onClick"])]),P("div",ql,[me(Al,{upgrades:z(r),"can-afford":z(h),"format-number":z(g),onBuyUpgrade:z(f)},null,8,["upgrades","can-afford","format-number","onBuyUpgrade"]),me(Wl,{"total-jellyfish":z(s),"jellyfish-per-second":z(n),"click-power":z(i),upgrades:z(r),achievements:z(o),"format-number":z(g),onSaveGame:z(d),onResetGame:z(a)},null,8,["total-jellyfish","jellyfish-per-second","click-power","upgrades","achievements","format-number","onSaveGame","onResetGame"])])]),F[1]||(F[1]=P("footer",{class:"app-footer"},[P("p",null,"🌊 Marine Biology Research • Made with Vue 3 + TypeScript 🧬")],-1))]))}}),zl=rs(Yl,[["__scopeId","data-v-c04cf9a5"]]);il(zl).mount("#app");
