# 🪼 Immortal Jelly Research Lab

A unique incremental/idle game inspired by the immortal jellyfish (Turri<PERSON><PERSON> do<PERSON>) built with Vue 3, TypeScript, and Vite. Study marine biology, cultivate immortal jellyfish, and unlock the secrets of eternal life!

## 🎮 Game Features

- **Jellyfish Cultivation**: Click the immortal jellyfish to stimulate cellular regeneration
- **Marine Ecosystem**: Build plankton colonies, coral reefs, and deep sea trenches
- **Scientific Research**: Purchase biological upgrades and genetic multipliers
- **Achievements**: Unlock marine biology achievements as you progress
- **Save System**: Your research progress is automatically saved to localStorage
- **Responsive Design**: Study on desktop or mobile devices

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone https://github.com/DavidSikora88/immortaljelly.git
cd immortaljelly
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

## 🏗️ Building for Production

```bash
npm run build
```

The built files will be in the `dist` directory.

## 🌐 GitHub Pages Deployment

This project is configured for automatic deployment to GitHub Pages:

1. Push your code to the `main` branch
2. GitHub Actions will automatically build and deploy your game
3. Your game will be available at `https://DavidSikora88.github.io/immortaljelly/`

### Manual Deployment

You can also deploy manually using:

```bash
npm run deploy
```

## 🎯 Game Mechanics

### Marine Ecosystem Components
- **Plankton Colony**: Microscopic organisms that feed jellyfish (0.1 jellyfish/sec)
- **Polyp Garden**: Juvenile jellyfish that grow into adults (1 jellyfish/sec)
- **Coral Reef**: Thriving ecosystem that nurtures jellyfish (8 jellyfish/sec)
- **Deep Sea Trench**: Ancient depths where immortal jellyfish multiply (47 jellyfish/sec)
- **Marine Biology Lab**: Scientific facility that cultivates immortal cells (260 jellyfish/sec)

### Research Upgrades
- **Bioluminescent Touch**: Enhance your regeneration stimulation power
- **Immortality Gene**: Boost all jellyfish reproduction rates

### Scientific Achievements
- **First Colony**: Cultivate your first 100 immortal jellyfish
- **Immortal Swarm**: Reach 1,000 total jellyfish
- **Ecosystem Builder**: Own 10 of any ecosystem component
- **Ocean Master**: Achieve 10,000 total jellyfish

## 🛠️ Tech Stack

- **Vue 3**: Progressive JavaScript framework
- **TypeScript**: Type-safe JavaScript
- **Vite**: Fast build tool and dev server
- **GitHub Actions**: Automated CI/CD
- **GitHub Pages**: Free hosting

## 📱 Browser Support

- Chrome/Edge 88+
- Firefox 85+
- Safari 14+

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🌊 About the Immortal Jellyfish

The Turritopsis dohrnii, known as the "immortal jellyfish," is a real species that can theoretically live forever by reversing its aging process and returning to its juvenile polyp stage. This fascinating creature inspired our game's theme of eternal life and regeneration.

## 🎉 Start Your Research!

Begin your marine biology research and unlock the secrets of immortality! 🪼🧬
