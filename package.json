{"name": "<PERSON><PERSON><PERSON>", "private": true, "version": "0.0.0", "type": "module", "homepage": "https://davidsikora88.github.io/immortaljelly", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "deploy": "npm run build && gh-pages -d docs", "lint": "eslint src --ext .ts,.vue"}, "dependencies": {"vue": "^3.5.13"}, "devDependencies": {"@types/node": "^22.15.29", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "eslint": "^8.57.1", "eslint-plugin-vue": "^10.1.0", "gh-pages": "^6.3.0", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-eslint-parser": "^10.1.3", "vue-tsc": "^2.2.8"}}