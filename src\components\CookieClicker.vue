<template>
  <div class="jellyfish-container">
    <div class="jellyfish-stats">
      <h2>{{ formatNumber(jellyfish) }} immortal jellyfish</h2>
      <p v-if="jellyfishPerSecond > 0">{{ formatNumber(jellyfishPerSecond) }} per second</p>
    </div>

    <div class="jellyfish-wrapper">
      <button
        class="jellyfish-button"
        @click="handleClick"
        :class="{ 'jellyfish-clicked': isClicked }"
      >
        🪼
      </button>

      <!-- Click effect -->
      <div
        v-for="effect in clickEffects"
        :key="effect.id"
        class="click-effect"
        :style="{
          left: effect.x + 'px',
          top: effect.y + 'px',
          opacity: effect.opacity
        }"
      >
        +{{ formatNumber(clickPower) }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface ClickEffect {
  id: number
  x: number
  y: number
  opacity: number
}

defineProps<{
  jellyfish: number
  jellyfishPerSecond: number
  clickPower: number
  formatNumber: (num: number) => string
}>()

const emit = defineEmits<{
  click: []
}>()

const isClicked = ref(false)
const clickEffects = ref<ClickEffect[]>([])
let effectId = 0

const handleClick = async (event: MouseEvent) => {
  emit('click')
  
  // Visual feedback
  isClicked.value = true
  setTimeout(() => {
    isClicked.value = false
  }, 100)
  
  // Create click effect
  const rect = (event.target as HTMLElement).getBoundingClientRect()
  const effect: ClickEffect = {
    id: effectId++,
    x: event.clientX - rect.left,
    y: event.clientY - rect.top,
    opacity: 1
  }
  
  clickEffects.value.push(effect)
  
  // Animate effect
  const animateEffect = () => {
    const effectIndex = clickEffects.value.findIndex(e => e.id === effect.id)
    if (effectIndex === -1) return
    
    clickEffects.value[effectIndex].y -= 2
    clickEffects.value[effectIndex].opacity -= 0.02
    
    if (clickEffects.value[effectIndex].opacity <= 0) {
      clickEffects.value.splice(effectIndex, 1)
    } else {
      requestAnimationFrame(animateEffect)
    }
  }
  
  requestAnimationFrame(animateEffect)
}
</script>

<style scoped>
.jellyfish-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
}

.jellyfish-stats {
  text-align: center;
  margin-bottom: 2rem;
}

.jellyfish-stats h2 {
  font-size: 2.5rem;
  margin: 0;
  color: #1e3a8a;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  background: linear-gradient(135deg, #1e40af, #3b82f6, #06b6d4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.jellyfish-stats p {
  font-size: 1.2rem;
  margin: 0.5rem 0 0 0;
  color: #0891b2;
}

.jellyfish-wrapper {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.jellyfish-button {
  font-size: 8rem;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.1), rgba(6, 182, 212, 0.1));
  border: 3px solid rgba(59, 130, 246, 0.3);
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 50%;
  padding: 1rem;
  user-select: none;
  position: relative;
  z-index: 1;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.jellyfish-button:hover {
  transform: scale(1.05);
  filter: brightness(1.2);
  box-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
}

.jellyfish-button:active,
.jellyfish-clicked {
  transform: scale(0.95);
}

.click-effect {
  position: absolute;
  font-size: 1.5rem;
  font-weight: bold;
  color: #06b6d4;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
  pointer-events: none;
  z-index: 2;
  transition: opacity 0.1s ease;
}

@media (max-width: 768px) {
  .jellyfish-button {
    font-size: 6rem;
  }

  .jellyfish-stats h2 {
    font-size: 2rem;
  }
}
</style>
