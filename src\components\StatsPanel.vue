<template>
  <div class="stats-panel">
    <h3>Research Data</h3>

    <div class="stats-grid">
      <div class="stat-item">
        <span class="stat-label">Total Jellyfish Cultivated:</span>
        <span class="stat-value">{{ formatNumber(totalJellyfish) }}</span>
      </div>

      <div class="stat-item">
        <span class="stat-label">Reproduction Rate:</span>
        <span class="stat-value">{{ formatNumber(jellyfishPerSecond) }}/sec</span>
      </div>

      <div class="stat-item">
        <span class="stat-label">Regeneration Power:</span>
        <span class="stat-value">{{ formatNumber(clickPower) }}</span>
      </div>

      <div class="stat-item">
        <span class="stat-label">Ecosystem Components:</span>
        <span class="stat-value">{{ totalBuildings }}</span>
      </div>
    </div>

    <div class="achievements-section" v-if="achievements.length > 0">
      <h4>Achievements</h4>
      <div class="achievements-list">
        <div 
          v-for="achievement in achievements" 
          :key="achievement"
          class="achievement-item"
          :title="getAchievementDescription(achievement)"
        >
          {{ getAchievementIcon(achievement) }}
          <span>{{ getAchievementName(achievement) }}</span>
        </div>
      </div>
    </div>

    <div class="controls-section">
      <button @click="saveGame" class="control-button save-button">
        💾 Save Game
      </button>
      <button @click="showResetConfirm = true" class="control-button reset-button">
        🔄 Reset Game
      </button>
    </div>

    <!-- Reset confirmation modal -->
    <div v-if="showResetConfirm" class="modal-overlay" @click="showResetConfirm = false">
      <div class="modal-content" @click.stop>
        <h4>Reset Game?</h4>
        <p>This will permanently delete all your progress. Are you sure?</p>
        <div class="modal-buttons">
          <button @click="showResetConfirm = false" class="cancel-button">Cancel</button>
          <button @click="confirmReset" class="confirm-button">Reset</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { Upgrade } from '../composables/useGame'

const props = defineProps<{
  totalJellyfish: number
  jellyfishPerSecond: number
  clickPower: number
  upgrades: Upgrade[]
  achievements: string[]
  formatNumber: (num: number) => string
}>()

const emit = defineEmits<{
  saveGame: []
  resetGame: []
}>()

const showResetConfirm = ref(false)

const totalBuildings = computed(() => {
  return props.upgrades
    .filter(u => u.type === 'auto')
    .reduce((sum, u) => sum + u.owned, 0)
})

const saveGame = () => {
  emit('saveGame')
  // Visual feedback could be added here
}

const confirmReset = () => {
  emit('resetGame')
  showResetConfirm.value = false
}

const getAchievementName = (achievement: string): string => {
  const names: Record<string, string> = {
    'first_colony': 'First Colony',
    'immortal_swarm': 'Immortal Swarm',
    'ecosystem_builder': 'Ecosystem Builder',
    'ocean_master': 'Ocean Master'
  }
  return names[achievement] || achievement
}

const getAchievementDescription = (achievement: string): string => {
  const descriptions: Record<string, string> = {
    'first_colony': 'Cultivate your first 100 immortal jellyfish',
    'immortal_swarm': 'Reach 1,000 total jellyfish',
    'ecosystem_builder': 'Own 10 of any ecosystem component',
    'ocean_master': 'Achieve 10,000 total jellyfish'
  }
  return descriptions[achievement] || ''
}

const getAchievementIcon = (achievement: string): string => {
  const icons: Record<string, string> = {
    'first_colony': '🌊',
    'immortal_swarm': '🪼',
    'ecosystem_builder': '🪸',
    'ocean_master': '🌀'
  }
  return icons[achievement] || '🏆'
}
</script>

<style scoped>
.stats-panel {
  background: linear-gradient(135deg, rgba(30, 58, 138, 0.1), rgba(6, 182, 212, 0.1));
  backdrop-filter: blur(10px);
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.stats-panel h3 {
  margin: 0 0 1rem 0;
  color: #1e40af;
  text-align: center;
  font-size: 1.5rem;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.stats-grid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  border-radius: 6px;
  border-left: 4px solid #06b6d4;
}

.stat-label {
  font-size: 0.9rem;
  color: #475569;
}

.stat-value {
  font-weight: bold;
  color: #1e40af;
  font-size: 1rem;
}

.achievements-section {
  margin-bottom: 1.5rem;
}

.achievements-section h4 {
  margin: 0 0 0.75rem 0;
  color: #8B4513;
  font-size: 1.2rem;
}

.achievements-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border-radius: 6px;
  color: #333;
  font-size: 0.9rem;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.controls-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.control-button {
  padding: 0.75rem;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
}

.save-button {
  background: #4CAF50;
  color: white;
}

.save-button:hover {
  background: #45a049;
  transform: translateY(-1px);
}

.reset-button {
  background: #f44336;
  color: white;
}

.reset-button:hover {
  background: #da190b;
  transform: translateY(-1px);
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  max-width: 400px;
  width: 90%;
}

.modal-content h4 {
  margin: 0 0 1rem 0;
  color: #333;
}

.modal-content p {
  margin: 0 0 1.5rem 0;
  color: #666;
  line-height: 1.4;
}

.modal-buttons {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.cancel-button, .confirm-button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-button {
  background: #ddd;
  color: #333;
}

.cancel-button:hover {
  background: #ccc;
}

.confirm-button {
  background: #f44336;
  color: white;
}

.confirm-button:hover {
  background: #da190b;
}

@media (max-width: 768px) {
  .stats-panel {
    padding: 1rem;
  }
  
  .stat-item {
    padding: 0.4rem;
  }
  
  .stat-label, .stat-value {
    font-size: 0.85rem;
  }
}
</style>
