import { ref, onMounted, onUnmounted } from 'vue'

export interface Upgrade {
  id: string
  name: string
  description: string
  cost: number
  baseCost: number
  owned: number
  effect: number
  type: 'click' | 'auto' | 'multiplier'
}

export interface GameState {
  jellyfish: number
  totalJellyfish: number
  jellyfishPerSecond: number
  clickPower: number
  upgrades: Upgrade[]
  achievements: string[]
  lastSave: number
}

const SAVE_KEY = 'immortal-jelly-game'

export function useGame() {
  // Game state
  const jellyfish = ref(0)
  const totalJellyfish = ref(0)
  const jellyfishPerSecond = ref(0)
  const clickPower = ref(1)
  const achievements = ref<string[]>([])

  // Upgrades definition
  const upgrades = ref<Upgrade[]>([
    {
      id: 'plankton',
      name: 'Plankton Colony',
      description: 'Microscopic organisms that feed your jellyfish',
      cost: 15,
      baseCost: 15,
      owned: 0,
      effect: 0.1,
      type: 'auto'
    },
    {
      id: 'polyp',
      name: 'Polyp Garden',
      description: 'Juvenile jellyfish that grow into adults',
      cost: 100,
      baseCost: 100,
      owned: 0,
      effect: 1,
      type: 'auto'
    },
    {
      id: 'coral_reef',
      name: 'Coral Reef',
      description: 'A thriving ecosystem that nurtures jellyfish',
      cost: 1100,
      baseCost: 1100,
      owned: 0,
      effect: 8,
      type: 'auto'
    },
    {
      id: 'deep_trench',
      name: 'Deep Sea Trench',
      description: 'Ancient depths where immortal jellyfish multiply',
      cost: 12000,
      baseCost: 12000,
      owned: 0,
      effect: 47,
      type: 'auto'
    },
    {
      id: 'bio_lab',
      name: 'Marine Biology Lab',
      description: 'Scientific facility that cultivates immortal cells',
      cost: 130000,
      baseCost: 130000,
      owned: 0,
      effect: 260,
      type: 'auto'
    },
    {
      id: 'click_upgrade',
      name: 'Bioluminescent Touch',
      description: 'Your touch stimulates cellular regeneration',
      cost: 100,
      baseCost: 100,
      owned: 0,
      effect: 2,
      type: 'click'
    },
    {
      id: 'multiplier',
      name: 'Immortality Gene',
      description: 'Doubles all jellyfish reproduction',
      cost: 50000,
      baseCost: 50000,
      owned: 0,
      effect: 2,
      type: 'multiplier'
    }
  ])

  // Computed values
  const canAfford = (upgrade: Upgrade) => jellyfish.value >= upgrade.cost

  const formatNumber = (num: number): string => {
    if (num < 1000) return Math.floor(num).toString()
    if (num < 1000000) return (num / 1000).toFixed(1) + 'K'
    if (num < 1000000000) return (num / 1000000).toFixed(1) + 'M'
    if (num < 1000000000000) return (num / 1000000000).toFixed(1) + 'B'
    return (num / 1000000000000).toFixed(1) + 'T'
  }

  // Game actions
  const clickJellyfish = () => {
    const earned = clickPower.value
    jellyfish.value += earned
    totalJellyfish.value += earned
    checkAchievements()
  }

  const buyUpgrade = (upgradeId: string) => {
    const upgrade = upgrades.value.find(u => u.id === upgradeId)
    if (!upgrade || !canAfford(upgrade)) return

    jellyfish.value -= upgrade.cost
    upgrade.owned++

    // Update cost (increases by 15% each purchase)
    upgrade.cost = Math.ceil(upgrade.baseCost * Math.pow(1.15, upgrade.owned))

    updateGameStats()
    checkAchievements()
    saveGame()
  }

  const updateGameStats = () => {
    // Calculate jellyfish per second
    let jps = 0
    let clickMultiplier = 1
    let globalMultiplier = 1

    upgrades.value.forEach(upgrade => {
      if (upgrade.type === 'auto') {
        jps += upgrade.owned * upgrade.effect
      } else if (upgrade.type === 'click') {
        clickMultiplier *= Math.pow(upgrade.effect, upgrade.owned)
      } else if (upgrade.type === 'multiplier') {
        globalMultiplier *= Math.pow(upgrade.effect, upgrade.owned)
      }
    })

    jellyfishPerSecond.value = jps * globalMultiplier
    clickPower.value = clickMultiplier * globalMultiplier
  }

  const checkAchievements = () => {
    const newAchievements: string[] = []

    if (totalJellyfish.value >= 100 && !achievements.value.includes('first_colony')) {
      newAchievements.push('first_colony')
    }
    if (totalJellyfish.value >= 1000 && !achievements.value.includes('immortal_swarm')) {
      newAchievements.push('immortal_swarm')
    }
    if (upgrades.value.some(u => u.owned >= 10) && !achievements.value.includes('ecosystem_builder')) {
      newAchievements.push('ecosystem_builder')
    }
    if (totalJellyfish.value >= 10000 && !achievements.value.includes('ocean_master')) {
      newAchievements.push('ocean_master')
    }

    achievements.value.push(...newAchievements)
  }

  // Save/Load system
  const saveGame = () => {
    const gameState: GameState = {
      jellyfish: jellyfish.value,
      totalJellyfish: totalJellyfish.value,
      jellyfishPerSecond: jellyfishPerSecond.value,
      clickPower: clickPower.value,
      upgrades: upgrades.value,
      achievements: achievements.value,
      lastSave: Date.now()
    }
    localStorage.setItem(SAVE_KEY, JSON.stringify(gameState))
  }

  const loadGame = () => {
    const saved = localStorage.getItem(SAVE_KEY)
    if (!saved) return

    try {
      const gameState: GameState = JSON.parse(saved)
      jellyfish.value = gameState.jellyfish || 0
      totalJellyfish.value = gameState.totalJellyfish || 0
      achievements.value = gameState.achievements || []

      // Merge saved upgrades with current upgrade definitions
      if (gameState.upgrades) {
        gameState.upgrades.forEach(savedUpgrade => {
          const currentUpgrade = upgrades.value.find(u => u.id === savedUpgrade.id)
          if (currentUpgrade) {
            currentUpgrade.owned = savedUpgrade.owned
            currentUpgrade.cost = savedUpgrade.cost
          }
        })
      }

      updateGameStats()
    } catch (error) {
      console.error('Failed to load game:', error)
    }
  }

  const resetGame = () => {
    jellyfish.value = 0
    totalJellyfish.value = 0
    achievements.value = []
    upgrades.value.forEach(upgrade => {
      upgrade.owned = 0
      upgrade.cost = upgrade.baseCost
    })
    updateGameStats()
    localStorage.removeItem(SAVE_KEY)
  }

  // Game loop
  let gameInterval: number | null = null

  const startGameLoop = () => {
    gameInterval = setInterval(() => {
      if (jellyfishPerSecond.value > 0) {
        const earned = jellyfishPerSecond.value / 10 // 10 FPS
        jellyfish.value += earned
        totalJellyfish.value += earned
      }

      // Auto-save every 30 seconds
      if (Date.now() % 30000 < 100) {
        saveGame()
      }
    }, 100)
  }

  const stopGameLoop = () => {
    if (gameInterval) {
      clearInterval(gameInterval)
      gameInterval = null
    }
  }

  // Lifecycle
  onMounted(() => {
    loadGame()
    startGameLoop()
  })

  onUnmounted(() => {
    stopGameLoop()
    saveGame()
  })

  return {
    // State
    jellyfish,
    totalJellyfish,
    jellyfishPerSecond,
    clickPower,
    upgrades,
    achievements,

    // Actions
    clickJellyfish,
    buyUpgrade,
    saveGame,
    loadGame,
    resetGame,

    // Utilities
    canAfford,
    formatNumber
  }
}
