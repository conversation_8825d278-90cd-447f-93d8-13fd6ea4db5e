<script setup lang="ts">
import { useGame } from './composables/useGame'
import CookieClicker from './components/CookieClicker.vue'
import UpgradeShop from './components/UpgradeShop.vue'
import StatsPanel from './components/StatsPanel.vue'

const {
  jellyfish,
  totalJellyfish,
  jellyfishPerSecond,
  clickPower,
  upgrades,
  achievements,
  clickJellyfish,
  buyUpgrade,
  saveGame,
  resetGame,
  canAfford,
  formatNumber
} = useGame()
</script>

<template>
  <div class="app">
    <header class="app-header">
      <h1>🪼 Immortal Jelly Research Lab 🧬</h1>
      <p class="subtitle">Study the immortal jellyfish and unlock the secrets of eternal life!</p>
    </header>

    <main class="app-main">
      <div class="game-area">
        <CookieClicker
          :jellyfish="jellyfish"
          :jellyfish-per-second="jellyfishPerSecond"
          :click-power="clickPower"
          :format-number="formatNumber"
          @click="clickJellyfish"
        />
      </div>

      <div class="sidebar">
        <UpgradeShop
          :upgrades="upgrades"
          :can-afford="canAfford"
          :format-number="formatNumber"
          @buy-upgrade="buyUpgrade"
        />

        <StatsPanel
          :total-jellyfish="totalJellyfish"
          :jellyfish-per-second="jellyfishPerSecond"
          :click-power="clickPower"
          :upgrades="upgrades"
          :achievements="achievements"
          :format-number="formatNumber"
          @save-game="saveGame"
          @reset-game="resetGame"
        />
      </div>
    </main>

    <footer class="app-footer">
      <p>🌊 Marine Biology Research • Made with Vue 3 + TypeScript 🧬</p>
    </footer>
  </div>
</template>

<style scoped>
.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f172a, #1e293b, #0891b2, #06b6d4);
  font-family: 'Arial', sans-serif;
}

.app-header {
  text-align: center;
  padding: 2rem 1rem 1rem 1rem;
  background: rgba(30, 58, 138, 0.1);
  backdrop-filter: blur(10px);
}

.app-header h1 {
  margin: 0;
  font-size: 2.5rem;
  background: linear-gradient(135deg, #1e40af, #3b82f6, #06b6d4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  font-weight: bold;
}

.subtitle {
  margin: 0.5rem 0 0 0;
  font-size: 1.1rem;
  color: #0891b2;
  font-style: italic;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.app-main {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 2rem;
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.game-area {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
}

.sidebar {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.app-footer {
  text-align: center;
  padding: 2rem;
  color: #0891b2;
  font-size: 0.9rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

@media (max-width: 1024px) {
  .app-main {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .sidebar {
    order: -1;
  }

  .app-header h1 {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .app-main {
    padding: 1rem;
  }

  .app-header {
    padding: 1rem;
  }

  .app-header h1 {
    font-size: 1.8rem;
  }

  .subtitle {
    font-size: 1rem;
  }
}
</style>
